# coding: utf-8
"""
OCR管理器模块

该模块实现了OCR识别管理器，统一管理多个OCR引擎，提供智能切换和容错机制。

主要功能：
- 管理多个OCR引擎
- 智能引擎选择和切换
- 提供容错和重试机制
- 支持配置管理

类说明：
- OCRManager: OCR识别管理器

作者: 小帅工具箱
版本: v1.0
"""

import time
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .interface import IOCREngine, OCRException, OCRResult
from ..ddddocr.engine import DdddocrEngine
from ..baidu.engine import BaiduOCREngine


class OCREngineType(Enum):
    """OCR引擎类型枚举"""
    DDDDOCR = "ddddocr"
    BAIDU = "baidu_ocr"


class OCRManager:
    """
    OCR识别管理器
    
    统一管理多个OCR引擎，提供智能选择、容错和重试机制。
    """
    
    def __init__(self):
        """初始化OCR管理器"""
        self.engines: Dict[str, IOCREngine] = {}
        self.engine_configs: Dict[str, Dict[str, Any]] = {}
        self.primary_engine = OCREngineType.DDDDOCR.value
        self.fallback_engine = OCREngineType.BAIDU.value
        self.confidence_threshold = 0.6
        self.max_retries = 3
        self.retry_delay = 1.0
        self.is_initialized = False
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'engine_usage': {},
            'average_processing_time': 0.0
        }
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化OCR管理器
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 更新配置
            self.primary_engine = config.get('primary_engine', self.primary_engine)
            self.fallback_engine = config.get('fallback_engine', self.fallback_engine)
            self.confidence_threshold = config.get('confidence_threshold', self.confidence_threshold)
            self.max_retries = config.get('max_retries', self.max_retries)
            self.retry_delay = config.get('retry_delay', self.retry_delay)
            
            # 保存引擎配置
            self.engine_configs = config.get('engines', {})
            
            # 初始化引擎
            await self._initialize_engines()
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            raise OCRException(f"OCR管理器初始化失败: {str(e)}")
    
    async def _initialize_engines(self) -> None:
        """初始化所有OCR引擎"""
        # 初始化Ddddocr引擎
        if self.primary_engine == OCREngineType.DDDDOCR.value or self.fallback_engine == OCREngineType.DDDDOCR.value:
            ddddocr_engine = DdddocrEngine()
            ddddocr_config = self.engine_configs.get('ddddocr', {})
            try:
                await ddddocr_engine.initialize(ddddocr_config)
                self.engines[OCREngineType.DDDDOCR.value] = ddddocr_engine
                self.stats['engine_usage'][OCREngineType.DDDDOCR.value] = 0
            except Exception as e:
                print(f"Ddddocr引擎初始化失败: {e}")
        
        # 初始化百度OCR引擎
        if self.primary_engine == OCREngineType.BAIDU.value or self.fallback_engine == OCREngineType.BAIDU.value:
            baidu_engine = BaiduOCREngine()
            baidu_config = self.engine_configs.get('baidu', {})
            try:
                if baidu_config.get('api_key') and baidu_config.get('secret_key'):
                    await baidu_engine.initialize(baidu_config)
                    self.engines[OCREngineType.BAIDU.value] = baidu_engine
                    self.stats['engine_usage'][OCREngineType.BAIDU.value] = 0
                else:
                    print("百度OCR配置不完整，跳过初始化")
            except Exception as e:
                print(f"百度OCR引擎初始化失败: {e}")
    
    async def recognize_captcha(self, image_data: bytes) -> OCRResult:
        """
        识别验证码
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            OCRResult: 识别结果
            
        Raises:
            OCRException: 所有引擎都失败时抛出异常
        """
        if not self.is_initialized:
            raise OCRException("OCR管理器未初始化")
        
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        # 尝试主引擎
        result = await self._try_engine(self.primary_engine, image_data)
        if result and result.is_valid(self.confidence_threshold):
            self._update_stats(result, start_time)
            return result
        
        # 主引擎失败，尝试备用引擎
        if self.fallback_engine != self.primary_engine:
            result = await self._try_engine(self.fallback_engine, image_data)
            if result and result.is_valid(self.confidence_threshold):
                self._update_stats(result, start_time)
                return result
        
        # 所有引擎都失败
        self.stats['failed_requests'] += 1
        raise OCRException("所有OCR引擎都无法识别验证码")
    
    async def _try_engine(self, engine_name: str, image_data: bytes) -> Optional[OCRResult]:
        """
        尝试使用指定引擎识别
        
        Args:
            engine_name: 引擎名称
            image_data: 图片数据
            
        Returns:
            Optional[OCRResult]: 识别结果，失败时返回None
        """
        engine = self.engines.get(engine_name)
        if not engine or not engine.is_available():
            return None
        
        for attempt in range(self.max_retries):
            try:
                start_time = time.time()
                text = await engine.recognize(image_data)
                processing_time = time.time() - start_time
                
                result = OCRResult(
                    text=text,
                    confidence=engine.get_confidence(),
                    engine_name=engine_name,
                    processing_time=processing_time
                )
                
                # 更新引擎使用统计
                self.stats['engine_usage'][engine_name] += 1
                
                return result
                
            except Exception as e:
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    print(f"引擎 {engine_name} 识别失败: {e}")
        
        return None
    
    def _update_stats(self, result: OCRResult, start_time: float) -> None:
        """更新统计信息"""
        self.stats['successful_requests'] += 1
        
        # 更新平均处理时间
        total_time = time.time() - start_time
        current_avg = self.stats['average_processing_time']
        total_successful = self.stats['successful_requests']
        
        self.stats['average_processing_time'] = (
            (current_avg * (total_successful - 1) + total_time) / total_successful
        )
    
    async def batch_recognize(self, image_list: List[bytes]) -> List[OCRResult]:
        """
        批量识别验证码
        
        Args:
            image_list: 图片数据列表
            
        Returns:
            List[OCRResult]: 识别结果列表
        """
        tasks = [self.recognize_captcha(image_data) for image_data in image_list]
        results = []
        
        for task in asyncio.as_completed(tasks):
            try:
                result = await task
                results.append(result)
            except OCRException as e:
                results.append(OCRResult(
                    text="",
                    confidence=0.0,
                    engine_name="unknown",
                    metadata={'error': str(e)}
                ))
        
        return results
    
    def get_available_engines(self) -> List[str]:
        """
        获取可用的引擎列表
        
        Returns:
            List[str]: 可用引擎名称列表
        """
        return [name for name, engine in self.engines.items() if engine.is_available()]
    
    def get_engine_info(self, engine_name: str) -> Dict[str, Any]:
        """
        获取引擎信息
        
        Args:
            engine_name: 引擎名称
            
        Returns:
            Dict[str, Any]: 引擎信息
        """
        engine = self.engines.get(engine_name)
        if not engine:
            return {}
        
        return {
            'name': engine.get_engine_name(),
            'available': engine.is_available(),
            'confidence': engine.get_confidence(),
            'usage_count': self.stats['engine_usage'].get(engine_name, 0)
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        success_rate = 0.0
        if self.stats['total_requests'] > 0:
            success_rate = self.stats['successful_requests'] / self.stats['total_requests']
        
        return {
            **self.stats,
            'success_rate': success_rate,
            'available_engines': self.get_available_engines(),
            'primary_engine': self.primary_engine,
            'fallback_engine': self.fallback_engine
        }
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """
        设置置信度阈值
        
        Args:
            threshold: 置信度阈值
        """
        self.confidence_threshold = max(0.0, min(1.0, threshold))
    
    def switch_primary_engine(self, engine_name: str) -> bool:
        """
        切换主引擎
        
        Args:
            engine_name: 新的主引擎名称
            
        Returns:
            bool: 切换是否成功
        """
        if engine_name in self.engines and self.engines[engine_name].is_available():
            self.primary_engine = engine_name
            return True
        return False
    
    async def cleanup(self) -> None:
        """清理所有引擎资源"""
        for engine in self.engines.values():
            await engine.cleanup()
        
        self.engines.clear()
        self.is_initialized = False
