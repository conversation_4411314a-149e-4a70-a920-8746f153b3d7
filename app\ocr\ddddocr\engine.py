# coding: utf-8
"""
Ddddocr引擎模块

该模块实现了基于ddddocr库的OCR识别引擎，作为主要的验证码识别引擎。

主要功能：
- 实现ddddocr验证码识别
- 支持多种验证码类型
- 提供置信度评估
- 支持图像预处理

类说明：
- DdddocrEngine: Ddddocr OCR引擎实现

作者: 小帅工具箱
版本: v1.0
"""

import time
import asyncio
from typing import Optional, Dict, Any
from io import BytesIO

try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
except ImportError:
    DDDDOCR_AVAILABLE = False
    ddddocr = None

from ..common.interface import IOCREngine, OCRException, OCRResult


class DdddocrEngine(IOCREngine):
    """
    Ddddocr OCR引擎实现
    
    基于ddddocr库实现的验证码识别引擎，支持常见的验证码类型识别。
    """
    
    def __init__(self):
        """初始化Ddddocr引擎"""
        self.engine_name = "ddddocr"
        self.ocr_instance = None
        self.last_confidence = 0.0
        self.is_initialized = False
        
        # 配置参数
        self.config = {
            'use_gpu': False,
            'show_ad': False,
            'beta': False,
            'old': False
        }
    
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        初始化引擎
        
        Args:
            config: 引擎配置参数
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not DDDDOCR_AVAILABLE:
                raise OCRException("ddddocr库未安装", self.engine_name)
            
            # 更新配置
            if config:
                self.config.update(config)
            
            # 在线程池中初始化ddddocr实例（避免阻塞）
            loop = asyncio.get_event_loop()
            self.ocr_instance = await loop.run_in_executor(
                None, self._create_ocr_instance
            )
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            raise OCRException(f"Ddddocr引擎初始化失败: {str(e)}", self.engine_name)
    
    def _create_ocr_instance(self):
        """创建OCR实例（在线程池中执行）"""
        return ddddocr.DdddOcr(
            use_gpu=self.config.get('use_gpu', False),
            show_ad=self.config.get('show_ad', False),
            beta=self.config.get('beta', False),
            old=self.config.get('old', False)
        )
    
    async def recognize(self, image_data: bytes) -> str:
        """
        识别验证码
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            str: 识别结果文本
            
        Raises:
            OCRException: 识别失败时抛出异常
        """
        if not self.is_initialized or not self.ocr_instance:
            await self.initialize()
        
        try:
            start_time = time.time()
            
            # 在线程池中执行识别（避免阻塞）
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, self._recognize_sync, image_data
            )
            
            processing_time = time.time() - start_time
            
            # 简单的置信度评估
            self.last_confidence = self._estimate_confidence(result, processing_time)
            
            return result.strip()
            
        except Exception as e:
            raise OCRException(f"Ddddocr识别失败: {str(e)}", self.engine_name)
    
    def _recognize_sync(self, image_data: bytes) -> str:
        """同步识别方法（在线程池中执行）"""
        return self.ocr_instance.classification(image_data)
    
    def _estimate_confidence(self, result: str, processing_time: float) -> float:
        """
        估算识别置信度
        
        Args:
            result: 识别结果
            processing_time: 处理时间
            
        Returns:
            float: 置信度值
        """
        if not result or len(result.strip()) == 0:
            return 0.0
        
        confidence = 0.8  # 基础置信度
        
        # 根据结果长度调整置信度
        result_length = len(result.strip())
        if result_length >= 4:  # 常见验证码长度
            confidence += 0.1
        elif result_length < 3:
            confidence -= 0.2
        
        # 根据处理时间调整置信度
        if processing_time < 1.0:  # 快速识别通常更准确
            confidence += 0.05
        elif processing_time > 3.0:
            confidence -= 0.1
        
        # 检查是否包含特殊字符（可能是识别错误）
        if any(char in result for char in ['@', '#', '$', '%', '&', '*']):
            confidence -= 0.2
        
        # 检查是否全是数字或字母（常见验证码格式）
        if result.isalnum():
            confidence += 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def get_confidence(self) -> float:
        """
        获取识别置信度
        
        Returns:
            float: 置信度值，范围0.0-1.0
        """
        return self.last_confidence
    
    def get_engine_name(self) -> str:
        """
        获取引擎名称
        
        Returns:
            str: 引擎名称
        """
        return self.engine_name
    
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        return DDDDOCR_AVAILABLE and self.is_initialized
    
    async def cleanup(self) -> None:
        """清理引擎资源"""
        self.ocr_instance = None
        self.is_initialized = False
        self.last_confidence = 0.0
    
    def get_supported_formats(self) -> list:
        """
        获取支持的图片格式
        
        Returns:
            list: 支持的格式列表
        """
        return ['jpg', 'jpeg', 'png', 'bmp', 'gif']
    
    def preprocess_image(self, image_data: bytes) -> bytes:
        """
        图像预处理（可选）
        
        Args:
            image_data: 原始图像数据
            
        Returns:
            bytes: 预处理后的图像数据
        """
        # ddddocr通常不需要额外的预处理
        # 这里可以根据需要添加图像增强逻辑
        return image_data
    
    async def batch_recognize(self, image_list: list) -> list:
        """
        批量识别
        
        Args:
            image_list: 图像数据列表
            
        Returns:
            list: 识别结果列表
        """
        results = []
        for image_data in image_list:
            try:
                result = await self.recognize(image_data)
                results.append(OCRResult(
                    text=result,
                    confidence=self.get_confidence(),
                    engine_name=self.engine_name
                ))
            except OCRException as e:
                results.append(OCRResult(
                    text="",
                    confidence=0.0,
                    engine_name=self.engine_name,
                    metadata={'error': str(e)}
                ))
        return results
