{"API": {"CaptureEnabled": true, "RetryCount": 3, "Timeout": 30}, "Concurrency": {"ApiRateLimit": 60, "DefaultTimeout": 300, "MaxConcurrentTasks": 10, "MaxConcurrentUsers": 5, "MaxQueueSize": 100, "MaxWorkers": 4, "SchedulerInterval": 1.0}, "System": {"AsyncLogin": true, "AutoMinimize": true, "AutoStart": false, "CheckUpdatesOnStart": true, "ConcurrentCount": 2, "DelayTime": 1, "EnableNotifications": true, "RetryCount": 3, "SaveWindowState": true}, "StudyStrategy": {"AutoAddElective": true, "AutoNextCourse": true, "CompulsoryCourses": 10, "ElectiveCourses": 5, "EnableRandomDelay": true, "MaxStudyTimePerCourse": 1800, "MinStudyTimePerCourse": 300, "MonitorInterval": 5.0, "RandomDelayMax": 5, "RandomDelayMin": 1, "SkipCompletedCourses": true, "StudyMode": "sequential", "StudySpeedMultiplier": 1.0}, "Backup": {"BackupInterval": 24, "BackupPath": "backups", "CompressBackups": true, "EnableAutoBackup": true, "MaxBackupFiles": 10}, "OCR": {"BaiduApiKey": "", "BaiduSecretKey": "", "ConfidenceThreshold": 0.8, "EnablePreprocessing": true, "FallbackEngine": "baidu", "ImageEnhancement": true, "RetryCount": 3, "Timeout": 10, "PrimaryEngine": "ddddocr"}, "Website": {"BaseUrl": "https://study.jxgbwlxy.gov.cn", "LoginUrl": "https://study.jxgbwlxy.gov.cn/index"}, "Browser": {"BrowserType": "chromium", "DisableCSS": false, "DisableImages": false, "DisableJavaScript": false, "DownloadPath": "downloads", "ElementWaitTimeout": 10, "EnableDevTools": false, "Headless": false, "OCREngine": "ddddocr", "PageLoadTimeout": 30, "UserAgent": "", "UserDataDir": "", "WindowHeight": 1080, "WindowWidth": 1920}, "Logging": {"EnableConsoleLog": true, "EnableDatabaseLog": true, "EnableFileLog": true, "LogBackupCount": 5, "LogDir": "logs", "LogLevel": "INFO", "MaxDays": 30, "MaxLogFileSize": 10485760, "MaxSizeMB": 100}, "QFluentWidgets": {"ThemeColor": "#ff009faa", "ThemeMode": "Light"}}