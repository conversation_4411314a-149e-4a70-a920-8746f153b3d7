# coding: utf-8
"""
OCR识别模块

该模块提供验证码识别功能，支持多种OCR引擎和智能切换机制。

主要组件：
- common: 通用接口和管理器
- ddddocr: Ddddocr引擎实现
- baidu: 百度OCR引擎实现

主要功能：
- 验证码识别
- 多引擎支持
- 智能切换和容错
- 配置管理

作者: 小帅工具箱
版本: v1.0
"""

from .common.interface import IOCREngine, OCRException, OCRResult
from .common.manager import OCRManager, OCREngineType
from .ddddocr.engine import DdddocrEngine
from .baidu.engine import BaiduOCREngine

__version__ = "1.0.0"
__author__ = "小帅工具箱"

__all__ = [
    'IOCREngine',
    'OCRException',
    'OCRResult',
    'OCRManager',
    'OCREngineType',
    'DdddocrEngine',
    'BaiduOCREngine'
]

# 创建全局OCR管理器实例
ocr_manager = OCRManager()

async def initialize_ocr(config: dict) -> bool:
    """
    初始化OCR模块

    Args:
        config: OCR配置参数

    Returns:
        bool: 初始化是否成功
    """
    return await ocr_manager.initialize(config)

async def recognize_captcha(image_data: bytes) -> OCRResult:
    """
    识别验证码（便捷方法）

    Args:
        image_data: 图片二进制数据

    Returns:
        OCRResult: 识别结果
    """
    return await ocr_manager.recognize_captcha(image_data)

def get_ocr_stats() -> dict:
    """
    获取OCR统计信息（便捷方法）

    Returns:
        dict: 统计信息
    """
    return ocr_manager.get_stats()

async def cleanup_ocr() -> None:
    """
    清理OCR资源（便捷方法）
    """
    await ocr_manager.cleanup()
"""
OCR识别模块

该模块包含各种OCR识别引擎的实现，包括：
- Ddddocr引擎
- 百度OCR引擎
- OCR管理器

作者: 小帅工具箱
版本: v1.0
"""
