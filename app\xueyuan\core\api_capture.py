# coding: utf-8
"""
API捕获系统模块

该模块实现了学习平台API数据捕获功能，监听和捕获关键API响应数据。

主要功能：
- 监听目标API响应
- 捕获和存储API数据
- 提供数据访问接口
- 支持回调机制

类说明：
- APICapture: API捕获管理器

作者: 小帅工具箱
版本: v1.0
"""

import time
import json
import asyncio
from typing import Dict, Any, Optional, Callable, List
from urllib.parse import urlparse, parse_qs

from PySide6.QtCore import QObject, Signal


class APICapture(QObject):
    """
    API捕获管理器
    
    监听和捕获学习平台的关键API响应数据，提供统一的数据访问接口。
    """
    
    # 信号定义
    dataReceived = Signal(str, dict)  # API URL, 数据
    errorOccurred = Signal(str, str)  # API URL, 错误信息
    
    def __init__(self, parent=None):
        """初始化API捕获管理器"""
        super().__init__(parent)
        
        # 目标API列表
        self.target_apis = [
            "https://study.jxgbwlxy.gov.cn/api/report/myData/online",                    # 用户学习状态API
            "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student",  # 学生档案API
            "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew",  # 必修课程API
            "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"        # 选修课程API
        ]
        
        # 捕获的数据存储
        self.captured_data: Dict[str, Dict[str, Any]] = {}
        
        # 回调函数注册
        self.callbacks: Dict[str, List[Callable]] = {}
        
        # 配置参数
        self.config = {
            'max_data_age': 300,  # 数据最大有效期（秒）
            'auto_cleanup': True,  # 自动清理过期数据
            'debug_mode': False    # 调试模式
        }
        
        # 统计信息
        self.stats = {
            'total_captured': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'api_counts': {api: 0 for api in self.target_apis}
        }
    
    def is_target_api(self, url: str) -> bool:
        """
        判断是否为目标API
        
        Args:
            url: API URL
            
        Returns:
            bool: 是否为目标API
        """
        # 移除查询参数进行匹配
        base_url = url.split('?')[0]
        return base_url in self.target_apis
    
    async def handle_response(self, response) -> None:
        """
        处理API响应
        
        Args:
            response: Playwright响应对象
        """
        url = response.url
        
        if not self.is_target_api(url):
            return
        
        try:
            self.stats['total_captured'] += 1
            
            if response.status == 200:
                # 获取响应数据
                data = await response.json()
                
                # 存储捕获的数据
                self.captured_data[url] = {
                    'status': response.status,
                    'data': data,
                    'timestamp': time.time(),
                    'headers': dict(response.headers),
                    'url_params': self._parse_url_params(url)
                }
                
                self.stats['successful_captures'] += 1
                self.stats['api_counts'][url.split('?')[0]] += 1
                
                # 发送信号
                self.dataReceived.emit(url, data)
                
                # 触发回调函数
                await self._trigger_callbacks(url, data)
                
                if self.config['debug_mode']:
                    print(f"API数据捕获成功: {url}")
                    
            else:
                self.stats['failed_captures'] += 1
                error_msg = f"HTTP {response.status}"
                self.errorOccurred.emit(url, error_msg)
                
                if self.config['debug_mode']:
                    print(f"API响应错误: {url} - {error_msg}")
                    
        except Exception as e:
            self.stats['failed_captures'] += 1
            error_msg = f"处理响应失败: {str(e)}"
            self.errorOccurred.emit(url, error_msg)
            
            if self.config['debug_mode']:
                print(f"API捕获异常: {url} - {error_msg}")
    
    def _parse_url_params(self, url: str) -> Dict[str, Any]:
        """
        解析URL参数
        
        Args:
            url: 完整URL
            
        Returns:
            Dict[str, Any]: URL参数字典
        """
        parsed_url = urlparse(url)
        return parse_qs(parsed_url.query)
    
    async def _trigger_callbacks(self, url: str, data: Dict[str, Any]) -> None:
        """
        触发回调函数
        
        Args:
            url: API URL
            data: 响应数据
        """
        base_url = url.split('?')[0]
        callbacks = self.callbacks.get(base_url, [])
        
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(url, data)
                else:
                    callback(url, data)
            except Exception as e:
                if self.config['debug_mode']:
                    print(f"回调函数执行失败: {callback} - {e}")
    
    def register_callback(self, api_url: str, callback: Callable) -> None:
        """
        注册API回调函数
        
        Args:
            api_url: API URL
            callback: 回调函数
        """
        if api_url not in self.callbacks:
            self.callbacks[api_url] = []
        self.callbacks[api_url].append(callback)
    
    def unregister_callback(self, api_url: str, callback: Callable) -> bool:
        """
        取消注册回调函数
        
        Args:
            api_url: API URL
            callback: 回调函数
            
        Returns:
            bool: 是否成功取消注册
        """
        if api_url in self.callbacks and callback in self.callbacks[api_url]:
            self.callbacks[api_url].remove(callback)
            return True
        return False
    
    def get_user_status_data(self) -> Dict[str, Any]:
        """
        获取用户状态数据
        
        Returns:
            Dict[str, Any]: 用户状态数据
        """
        api_url = "https://study.jxgbwlxy.gov.cn/api/report/myData/online"
        return self._get_api_data(api_url)
    
    def get_student_info_data(self) -> Dict[str, Any]:
        """
        获取学生信息数据
        
        Returns:
            Dict[str, Any]: 学生信息数据
        """
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/student/studentArchives/student"
        return self._get_api_data(api_url)
    
    def get_compulsory_courses_data(self) -> Dict[str, Any]:
        """
        获取必修课程数据
        
        Returns:
            Dict[str, Any]: 必修课程数据
        """
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/years/yearsCourseware/annualPortalCourseListNew"
        return self._get_api_data(api_url)
    
    def get_elective_courses_data(self) -> Dict[str, Any]:
        """
        获取选修课程数据
        
        Returns:
            Dict[str, Any]: 选修课程数据
        """
        api_url = "https://study.jxgbwlxy.gov.cn/api/study/my/elective/myElectivesNew"
        return self._get_api_data(api_url)
    
    def _get_api_data(self, api_url: str) -> Dict[str, Any]:
        """
        获取指定API的数据
        
        Args:
            api_url: API URL
            
        Returns:
            Dict[str, Any]: API数据
        """
        # 查找匹配的URL（可能包含查询参数）
        for url, data_info in self.captured_data.items():
            if url.startswith(api_url):
                # 检查数据是否过期
                if self._is_data_valid(data_info):
                    return data_info.get('data', {}).get('data', {})
        
        return {}
    
    def _is_data_valid(self, data_info: Dict[str, Any]) -> bool:
        """
        检查数据是否有效
        
        Args:
            data_info: 数据信息
            
        Returns:
            bool: 数据是否有效
        """
        if not self.config['auto_cleanup']:
            return True
        
        timestamp = data_info.get('timestamp', 0)
        current_time = time.time()
        return (current_time - timestamp) <= self.config['max_data_age']
    
    def cleanup_expired_data(self) -> int:
        """
        清理过期数据
        
        Returns:
            int: 清理的数据条数
        """
        if not self.config['auto_cleanup']:
            return 0
        
        current_time = time.time()
        expired_urls = []
        
        for url, data_info in self.captured_data.items():
            timestamp = data_info.get('timestamp', 0)
            if (current_time - timestamp) > self.config['max_data_age']:
                expired_urls.append(url)
        
        for url in expired_urls:
            del self.captured_data[url]
        
        return len(expired_urls)
    
    def get_captured_apis(self) -> List[str]:
        """
        获取已捕获数据的API列表
        
        Returns:
            List[str]: API URL列表
        """
        return list(self.captured_data.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        success_rate = 0.0
        if self.stats['total_captured'] > 0:
            success_rate = self.stats['successful_captures'] / self.stats['total_captured']
        
        return {
            **self.stats,
            'success_rate': success_rate,
            'captured_apis': len(self.captured_data),
            'target_apis_count': len(self.target_apis)
        }
    
    def clear_all_data(self) -> None:
        """清理所有捕获的数据"""
        self.captured_data.clear()
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置配置参数
        
        Args:
            config: 配置参数
        """
        self.config.update(config)
