# coding: utf-8
"""
学习工具主界面模块

该模块定义了学习工具的主界面类 StudyMainInterface，使用 Pivot 导航结构。
主界面负责管理学习工具的各个子界面，包括学习控制、用户管理、进度监控、
日志查看和学习设置等功能模块。

主要功能：
- 创建和管理各个子界面
- 配置 Pivot 导航栏
- 处理界面切换和事件响应
- 管理学习工具的整体布局

类说明：
- StudyMainInterface: 学习工具主界面类，使用 Pivot 导航结构

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt, QSize
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget
from qfluentwidgets import (ScrollArea, Pivot, qrouter, SegmentedWidget,
                            FluentIcon as FIF)

from .study_control_interface import StudyControlInterface
from .user_manage_interface import UserManageInterface
from .progress_monitor_interface import ProgressMonitorInterface
from .log_view_interface import LogViewInterface
from .study_setting_interface import StudySettingInterface
from ...common.style_sheet import StyleSheet


class StudyMainInterface(ScrollArea):
    """
    学习工具主界面类
    
    使用 Pivot 导航结构管理各个子界面，提供统一的学习工具入口。
    包含学习控制、用户管理、进度监控、日志查看和学习设置等功能模块。
    """
    
    def __init__(self, parent=None):
        """
        初始化学习工具主界面
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("StudyMainInterface")

        # 应用样式表
        StyleSheet.STUDY_MAIN_INTERFACE.apply(self)
        
        # 创建主容器
        self.view = QWidget()
        self.vBoxLayout = QVBoxLayout(self.view)

        # 创建 Pivot 导航
        self.pivot = Pivot(self.view)

        # 创建堆叠窗口管理子界面
        self.stackedWidget = QStackedWidget(self.view)

        # 创建子界面
        self.studyControlInterface = StudyControlInterface(self.stackedWidget)
        self.userManageInterface = UserManageInterface(self.stackedWidget)
        self.progressMonitorInterface = ProgressMonitorInterface(self.stackedWidget)
        self.logViewInterface = LogViewInterface(self.stackedWidget)
        self.studySettingInterface = StudySettingInterface(self.stackedWidget)
        
        # 初始化界面
        self.initLayout()
        self.initNavigation()
        self.connectSignalToSlot()
    
    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(24, 20, 24, 24)
        self.vBoxLayout.setSpacing(20)

        # 标题卡片布局已取消

        # 添加导航
        self.vBoxLayout.addWidget(self.pivot, 0, Qt.AlignLeft)

        # 添加子界面到堆叠窗口
        self.stackedWidget.addWidget(self.studyControlInterface)
        self.stackedWidget.addWidget(self.userManageInterface)
        self.stackedWidget.addWidget(self.progressMonitorInterface)
        self.stackedWidget.addWidget(self.logViewInterface)
        self.stackedWidget.addWidget(self.studySettingInterface)

        # 添加堆叠窗口到主布局
        self.vBoxLayout.addWidget(self.stackedWidget)

        # 设置滚动区域
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 设置样式
        self.setStyleSheet("""
            StudyMainInterface {
                background-color: transparent;
                border: none;
            }
        """)
    
    def initNavigation(self):
        """初始化导航栏"""
        # 添加导航项
        self.addSubInterface(self.studyControlInterface, 'studyControl', '学习控制', FIF.PLAY)
        self.addSubInterface(self.userManageInterface, 'userManage', '用户管理', FIF.PEOPLE)
        self.addSubInterface(self.progressMonitorInterface, 'progressMonitor', '进度监控', FIF.PIE_SINGLE)
        self.addSubInterface(self.logViewInterface, 'logView', '日志查看', FIF.HISTORY)
        self.addSubInterface(self.studySettingInterface, 'studySetting', '学习设置', FIF.SETTING)

        # 设置默认选中项
        self.pivot.setCurrentItem(self.studyControlInterface.objectName())
        self.stackedWidget.setCurrentWidget(self.studyControlInterface)
        qrouter.setDefaultRouteKey(self.stackedWidget, self.studyControlInterface.objectName())
    
    def addSubInterface(self, widget: QWidget, objectName: str, text: str, icon=None):
        """
        添加子界面
        
        Args:
            widget: 子界面组件
            objectName: 对象名称
            text: 显示文本
            icon: 图标
        """
        widget.setObjectName(objectName)
        
        # 添加到 Pivot
        if icon:
            self.pivot.addItem(
                routeKey=objectName,
                text=text,
                onClick=lambda: self.showInterface(widget),
                icon=icon
            )
        else:
            self.pivot.addItem(
                routeKey=objectName,
                text=text,
                onClick=lambda: self.showInterface(widget)
            )
    
    def showInterface(self, widget: QWidget):
        """
        显示指定的界面

        Args:
            widget: 要显示的界面组件
        """
        # 使用堆叠窗口切换界面
        self.stackedWidget.setCurrentWidget(widget)
    
    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        # 连接 Pivot 的切换信号
        self.pivot.currentItemChanged.connect(self.onCurrentItemChanged)
    
    def onCurrentItemChanged(self, objectName: str):
        """
        当前项改变时的处理函数
        
        Args:
            objectName: 当前选中项的对象名称
        """
        # 根据对象名称显示对应界面
        if objectName == 'studyControl':
            self.showInterface(self.studyControlInterface)
        elif objectName == 'userManage':
            self.showInterface(self.userManageInterface)
        elif objectName == 'progressMonitor':
            self.showInterface(self.progressMonitorInterface)
        elif objectName == 'logView':
            self.showInterface(self.logViewInterface)
        elif objectName == 'studySetting':
            self.showInterface(self.studySettingInterface)
    
    def resizeEvent(self, e):
        """窗口大小改变事件"""
        super().resizeEvent(e)
        # 可以在这里添加响应式布局逻辑
