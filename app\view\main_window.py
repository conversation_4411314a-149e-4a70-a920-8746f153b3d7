# coding: utf-8
"""
主窗口模块

该模块定义了应用程序的主窗口类 MainWindow，继承自 MSFluentWindow。
主窗口负责管理整个应用程序的界面布局、导航系统、主题切换等核心功能。

主要功能：
- 创建和管理各个子界面（首页、布局、设置等）
- 配置导航栏和导航项
- 处理主题切换和系统主题监听
- 管理窗口效果（如云母效果、启动画面等）
- 处理信号槽连接和事件响应

类说明：
- MainWindow: 应用程序主窗口类，提供完整的窗口管理功能，使用Microsoft Store风格
"""

from PySide6.QtCore import QSize, QTimer
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QApplication

from qfluentwidgets import (NavigationItemPosition, MSFluentWindow,
                            SplashScreen, SystemThemeListener, isDarkTheme, toggleTheme)
from qfluentwidgets import FluentIcon as FIF


from .home_interface import HomeInterface
from .blank_interface import BlankInterface
from .setting_interface import SettingInterface
from ..xueyuan.view.study_main_interface import StudyMainInterface
from ..common.config import cfg
from ..common.signal_bus import signalBus
from ..common.translator import Translator
from ..common import resource  # 必需导入以注册Qt资源



class MainWindow(MSFluentWindow):
    """
    应用程序主窗口类

    继承自 MSFluentWindow，提供Microsoft Store风格的流畅设计界面。
    负责管理整个应用程序的界面结构、导航系统和主题设置。
    """

    def __init__(self):
        """
        初始化主窗口

        创建主窗口实例，初始化各个子界面，配置导航栏，
        启动系统主题监听器，并设置窗口效果。
        """
        super().__init__()
        self.initWindow()

        # 创建系统主题监听器
        self.themeListener = SystemThemeListener(self)

        # 创建子界面
        self.homeInterface = HomeInterface(self)
        self.blankInterface = BlankInterface(self)
        self.studyMainInterface = StudyMainInterface(self)
        self.settingInterface = SettingInterface(self)

        # MSFluentWindow 的导航界面默认已经有合适的样式，不需要额外设置亚克力效果
        # self.navigationInterface.setAcrylicEnabled(True)

        self.connectSignalToSlot()

        # 添加导航项到导航界面
        self.initNavigation()
        self.addThemeButton()
        self.splashScreen.finish()

        # 启动主题监听器
        self.themeListener.start()

    def connectSignalToSlot(self):
        """
        连接信号到槽函数

        建立信号与槽之间的连接，用于处理云母效果切换和页面切换等事件。
        """
        signalBus.micaEnableChanged.connect(self.setMicaEffectEnabled)

        signalBus.switchToPageSignal.connect(self.switchToPage)

    def initNavigation(self):
        """
        初始化导航界面

        添加各个子界面到导航栏，包括首页、布局界面、设置界面等。
        MSFluentWindow 使用 Microsoft Store 风格的导航界面。
        """
        # 添加导航项
        t = Translator()
        self.addSubInterface(self.homeInterface, FIF.HOME, self.tr('Home'))

        pos = NavigationItemPosition.SCROLL
        self.addSubInterface(self.studyMainInterface, FIF.EDUCATION, self.tr('学习工具'), position=pos)
        self.addSubInterface(self.blankInterface, FIF.DOCUMENT, self.tr('空白页面'), position=pos)

        # 添加设置界面到底部
        self.addSubInterface(
            self.settingInterface, FIF.SETTING, self.tr('Settings'), position=NavigationItemPosition.BOTTOM)

    def addThemeButton(self):
        """
        在导航界面中添加主题切换按钮

        在导航栏底部添加一个主题切换按钮，用于快速切换明暗主题。
        """
        # 添加主题切换按钮到导航界面底部
        self.navigationInterface.addItem(
            routeKey='theme',
            icon=FIF.CONSTRACT,
            text=self.tr('切换主题'),
            onClick=self.toggleTheme,
            selectable=False,
            position=NavigationItemPosition.BOTTOM
        )

    def initWindow(self):
        """
        初始化窗口设置

        设置窗口大小、图标、标题，启用云母效果，创建启动画面，
        并将窗口居中显示在屏幕上。
        """
        self.resize(960, 780)
        self.setMinimumWidth(760)
        self.setWindowIcon(QIcon(':/gallery/images/logo.png'))
        self.setWindowTitle('小帅工具箱')

        self.setMicaEffectEnabled(cfg.get(cfg.micaEnabled))

        # 创建启动画面
        self.splashScreen = SplashScreen(self.windowIcon(), self)
        self.splashScreen.setIconSize(QSize(106, 106))
        self.splashScreen.raise_()

        desktop = QApplication.screens()[0].availableGeometry()
        w, h = desktop.width(), desktop.height()
        self.move(w//2 - self.width()//2, h//2 - self.height()//2)
        self.show()
        QApplication.processEvents()

    def toggleTheme(self):
        """切换明暗主题"""
        toggleTheme(True)



    def switchToPage(self, routeKey):
        """
        切换到指定页面

        根据传入的路由键切换到对应的页面。

        Args:
            routeKey (str): 页面路由键
        """
        # 根据路由键切换到对应的页面
        if routeKey == 'blankInterface':
            self.switchTo(self.blankInterface)
        # 可以在这里添加更多页面的路由处理

    def resizeEvent(self, e):
        """
        窗口大小改变事件处理

        当窗口大小改变时，同步调整启动画面的大小。
        """
        super().resizeEvent(e)
        if hasattr(self, 'splashScreen'):
            self.splashScreen.resize(self.size())

    def closeEvent(self, e):
        """
        窗口关闭事件处理

        在窗口关闭时，终止主题监听器并清理资源。
        """
        self.themeListener.terminate()
        self.themeListener.deleteLater()
        super().closeEvent(e)

    def _onThemeChangedFinished(self):
        """
        主题切换完成后的处理

        主题切换完成后，重新设置云母效果以确保效果正确应用。
        """
        super()._onThemeChangedFinished()

        # 重试设置云母效果
        if self.isMicaEffectEnabled():
            QTimer.singleShot(100, lambda: self.windowEffect.setMicaEffect(self.winId(), isDarkTheme()))


