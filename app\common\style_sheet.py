# coding: utf-8
"""
样式表模块

该模块定义了应用程序的样式表管理系统，提供各个界面组件的
样式表文件路径管理和主题切换支持。

主要功能：
- 管理各个界面组件的样式表文件
- 支持明暗主题自动切换
- 提供样式表文件路径解析

类说明：
- StyleSheet: 样式表枚举类，定义各个组件的样式表文件
"""

from enum import Enum

from qfluentwidgets import StyleSheetBase, Theme, qconfig


class StyleSheet(StyleSheetBase, Enum):
    """
    样式表枚举类

    定义应用程序中各个界面组件对应的样式表文件，
    支持根据主题自动选择对应的样式文件。
    """

    LINK_CARD = "link_card"
    HOME_INTERFACE = "home_interface"
    SETTING_INTERFACE = "setting_interface"
    BLANK_INTERFACE = "blank_interface"
    STUDY_MAIN_INTERFACE = "study_main_interface"

    def path(self, theme=Theme.AUTO):
        """
        获取样式表文件路径

        Args:
            theme: 主题类型，默认为自动检测

        Returns:
            str: 样式表文件的资源路径
        """
        theme = qconfig.theme if theme == Theme.AUTO else theme
        return f":/gallery/qss/{theme.value.lower()}/{self.value}.qss"
