# coding:utf-8
"""
测试应用程序各个组件
"""

import sys
import traceback

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        print("1. 测试数据库模块...")
        from app.xueyuan.database.dao import user_dao, course_dao, log_dao
        print("   ✓ 数据库模块导入成功")
        
        print("2. 测试核心模块...")
        from app.xueyuan.core.study_engine import StudyEngine
        from app.xueyuan.core.thread_pool import StudyThreadPool
        from app.xueyuan.core.course_manager import CourseManager
        print("   ✓ 核心模块导入成功")
        
        print("3. 测试OCR模块...")
        from app.ocr import OCRManager
        print("   ✓ OCR模块导入成功")
        
        print("4. 测试界面模块...")
        from app.xueyuan.view.study_main_interface import StudyMainInterface
        from app.xueyuan.view.study_control_interface import StudyControlInterface
        from app.xueyuan.view.user_manage_interface import UserManageInterface
        print("   ✓ 界面模块导入成功")
        
        print("5. 测试配置模块...")
        from app.xueyuan.common.config import StudyConfig
        print("   ✓ 配置模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_database():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    
    try:
        from app.xueyuan.database.dao import user_dao
        
        # 测试获取用户列表
        users = user_dao.get_all_users()
        print(f"   ✓ 数据库连接成功，当前用户数量: {len(users)}")
        return True
        
    except Exception as e:
        print(f"   ✗ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置系统"""
    print("\n=== 测试配置系统 ===")
    
    try:
        from app.xueyuan.common.config import cfg
        
        # 测试配置读取
        async_login = cfg.asyncLogin.value
        concurrent_count = cfg.concurrentCount.value
        
        print(f"   ✓ 配置系统正常，异步登录: {async_login}, 并发数: {concurrent_count}")
        return True
        
    except Exception as e:
        print(f"   ✗ 配置系统失败: {e}")
        traceback.print_exc()
        return False

def test_gui():
    """测试GUI组件"""
    print("\n=== 测试GUI组件 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from app.xueyuan.view.study_main_interface import StudyMainInterface
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = StudyMainInterface()
        print("   ✓ 主窗口创建成功")
        
        # 测试界面组件
        print(f"   ✓ 学习控制界面: {window.studyControlInterface is not None}")
        print(f"   ✓ 用户管理界面: {window.userManageInterface is not None}")
        print(f"   ✓ 进度监控界面: {window.progressMonitorInterface is not None}")
        print(f"   ✓ 日志查看界面: {window.logViewInterface is not None}")
        print(f"   ✓ 学习设置界面: {window.studySettingInterface is not None}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ GUI组件测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("学习工具应用程序组件测试")
    print("=" * 50)
    
    results = []
    
    # 运行各项测试
    results.append(test_imports())
    results.append(test_database())
    results.append(test_config())
    results.append(test_gui())
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序组件正常。")
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
