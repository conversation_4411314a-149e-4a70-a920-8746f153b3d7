# coding: utf-8
"""
进度监控界面模块

该模块定义了进度监控界面，提供学习进度的可视化展示和统计分析。

主要功能：
- 学习进度图表展示
- 用户进度统计
- 课程完成情况分析
- 实时进度监控

类说明：
- ProgressMonitorInterface: 进度监控界面类

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout
from qfluentwidgets import (CardWidget, BodyLabel, SubtitleLabel, TitleLabel,
                            ProgressBar, ProgressRing, FluentIcon as FIF)


class ProgressMonitorInterface(QWidget):
    """
    进度监控界面类
    
    提供学习进度的可视化监控和统计分析功能。
    """
    
    def __init__(self, parent=None):
        """
        初始化进度监控界面
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("ProgressMonitorInterface")
        
        # 初始化界面
        self.initUI()
    
    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(24, 24, 24, 24)
        self.vBoxLayout.setSpacing(20)
        
        # 创建概览面板
        self.createOverviewPanel()
        
        # 创建详细进度面板
        self.createDetailProgressPanel()
        
        # 添加弹性空间
        self.vBoxLayout.addStretch()
    
    def createOverviewPanel(self):
        """创建概览面板"""
        self.overviewCard = CardWidget(self)
        self.overviewLayout = QVBoxLayout(self.overviewCard)
        self.overviewLayout.setContentsMargins(20, 20, 20, 20)
        self.overviewLayout.setSpacing(15)
        
        # 标题
        self.overviewTitle = SubtitleLabel("进度概览", self.overviewCard)
        self.overviewLayout.addWidget(self.overviewTitle)
        
        # 统计网格
        self.statsGrid = QGridLayout()
        self.statsGrid.setSpacing(20)
        
        # 总体进度环
        self.overallProgressRing = ProgressRing(self.overviewCard)
        self.overallProgressRing.setFixedSize(100, 100)
        self.overallProgressRing.setValue(0)
        self.overallProgressRing.setTextVisible(True)
        
        # 统计标签
        self.totalUsersLabel = BodyLabel("总用户数: 0", self.overviewCard)
        self.completedUsersLabel = BodyLabel("已完成用户: 0", self.overviewCard)
        self.inProgressUsersLabel = BodyLabel("学习中用户: 0", self.overviewCard)
        self.avgProgressLabel = BodyLabel("平均进度: 0%", self.overviewCard)
        
        # 添加到网格
        self.statsGrid.addWidget(self.overallProgressRing, 0, 0, 2, 1)
        self.statsGrid.addWidget(self.totalUsersLabel, 0, 1)
        self.statsGrid.addWidget(self.completedUsersLabel, 0, 2)
        self.statsGrid.addWidget(self.inProgressUsersLabel, 1, 1)
        self.statsGrid.addWidget(self.avgProgressLabel, 1, 2)
        
        self.overviewLayout.addLayout(self.statsGrid)
        self.vBoxLayout.addWidget(self.overviewCard)
    
    def createDetailProgressPanel(self):
        """创建详细进度面板"""
        self.detailCard = CardWidget(self)
        self.detailLayout = QVBoxLayout(self.detailCard)
        self.detailLayout.setContentsMargins(20, 20, 20, 20)
        self.detailLayout.setSpacing(15)
        
        # 标题
        self.detailTitle = SubtitleLabel("详细进度", self.detailCard)
        self.detailLayout.addWidget(self.detailTitle)
        
        # 进度条示例
        self.createProgressBars()
        
        self.vBoxLayout.addWidget(self.detailCard)
    
    def createProgressBars(self):
        """创建进度条示例"""
        # 必修课程进度
        self.compulsoryLabel = BodyLabel("必修课程进度", self.detailCard)
        self.compulsoryProgress = ProgressBar(self.detailCard)
        self.compulsoryProgress.setValue(0)
        
        # 选修课程进度
        self.electiveLabel = BodyLabel("选修课程进度", self.detailCard)
        self.electiveProgress = ProgressBar(self.detailCard)
        self.electiveProgress.setValue(0)
        
        # 总体学时进度
        self.totalCreditLabel = BodyLabel("总体学时进度", self.detailCard)
        self.totalCreditProgress = ProgressBar(self.detailCard)
        self.totalCreditProgress.setValue(0)
        
        # 添加到布局
        self.detailLayout.addWidget(self.compulsoryLabel)
        self.detailLayout.addWidget(self.compulsoryProgress)
        self.detailLayout.addWidget(self.electiveLabel)
        self.detailLayout.addWidget(self.electiveProgress)
        self.detailLayout.addWidget(self.totalCreditLabel)
        self.detailLayout.addWidget(self.totalCreditProgress)
