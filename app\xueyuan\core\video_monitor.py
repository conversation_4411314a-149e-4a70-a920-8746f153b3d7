# coding: utf-8
"""
视频学习监控器模块

该模块实现了视频播放监控、进度跟踪和课件完成检测功能。

主要功能：
- 视频播放状态监控
- 学习进度实时跟踪
- 课件完成状态检测
- 自动处理视频选择页面
- 学习状态更新和同步

类说明：
- VideoLearningMonitor: 视频学习监控器

作者: 小帅工具箱
版本: v1.0
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime

from PySide6.QtCore import QObject, Signal, QTimer

from ..common.config import cfg
from ..database.dao import course_dao


class VideoLearningMonitor(QObject):
    """
    视频学习监控器
    
    负责监控视频播放状态、跟踪学习进度和检测课件完成状态。
    """
    
    # 信号定义
    courseStarted = Signal(dict)  # 课程开始学习
    courseCompleted = Signal(dict)  # 课程学习完成
    progressUpdated = Signal(dict, dict)  # 课程信息, 进度信息
    videoStateChanged = Signal(dict, str)  # 课程信息, 视频状态
    errorOccurred = Signal(str, str)  # 错误类型, 错误消息
    logMessage = Signal(str, str)  # 日志级别, 消息内容
    
    def __init__(self, study_engine, parent=None):
        """
        初始化视频学习监控器
        
        Args:
            study_engine: 学习引擎实例
            parent: 父对象
        """
        super().__init__(parent)
        self.engine = study_engine
        self.current_course = None
        self.monitoring = False
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._check_video_progress)
        self.monitor_interval = 30000  # 30秒检查一次
        
        # 监控状态
        self.last_progress = 0
        self.last_check_time = 0
        self.stuck_count = 0  # 进度卡住计数
        self.max_stuck_count = 5  # 最大卡住次数
    
    async def start_course_learning(self, course_data: Dict[str, Any]) -> bool:
        """
        开始课程学习
        
        Args:
            course_data: 课程数据
            
        Returns:
            bool: 是否成功开始学习
        """
        try:
            self.current_course = course_data
            video_url = course_data.get("videoUrl", "")
            
            if not video_url:
                error_msg = f"课程 {course_data.get('name', '')} 没有视频URL"
                self.errorOccurred.emit("NO_VIDEO_URL", error_msg)
                self.logMessage.emit("ERROR", error_msg)
                return False
            
            self.logMessage.emit("INFO", f"开始学习课程: {course_data.get('name', '')}")
            
            # 打开视频播放页面
            await self.engine.page.goto(video_url)
            await self.engine.page.wait_for_load_state("networkidle")
            
            # 处理温馨提示页面
            if "videoChoose" in self.engine.page.url:
                await self.handle_video_choose_page()
            
            # 更新用户状态
            await self.engine.update_user_status_from_api()
            
            # 发送课程开始信号
            self.courseStarted.emit(course_data)
            
            # 开始监控视频学习
            return await self.monitor_video_learning()
            
        except Exception as e:
            error_msg = f"开始课程学习失败: {str(e)}"
            self.errorOccurred.emit("START_LEARNING_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False
    
    async def handle_video_choose_page(self):
        """
        处理温馨提示页面
        """
        try:
            self.logMessage.emit("DEBUG", "检测到视频选择页面，正在处理...")
            
            # 等待页面加载完成
            await asyncio.sleep(2)
            
            # 查找并点击选择内容
            choose_content = await self.engine.page.query_selector('.choose-content')
            if choose_content:
                await choose_content.click()
                await self.engine.page.wait_for_load_state("networkidle")
                self.logMessage.emit("DEBUG", "成功处理视频选择页面")
            else:
                # 尝试其他可能的选择器
                selectors = [
                    '.video-choose-content',
                    '.content-item',
                    '.course-content',
                    'button[type="button"]'
                ]
                
                for selector in selectors:
                    element = await self.engine.page.query_selector(selector)
                    if element:
                        await element.click()
                        await self.engine.page.wait_for_load_state("networkidle")
                        self.logMessage.emit("DEBUG", f"使用选择器 {selector} 处理视频选择页面")
                        break
                else:
                    self.logMessage.emit("WARNING", "未找到视频选择页面的处理元素")
                    
        except Exception as e:
            self.logMessage.emit("WARNING", f"处理视频选择页面失败: {str(e)}")
    
    async def monitor_video_learning(self) -> bool:
        """
        监控视频学习过程
        
        Returns:
            bool: 学习是否完成
        """
        try:
            self.monitoring = True
            self.last_progress = 0
            self.last_check_time = time.time()
            self.stuck_count = 0
            
            self.logMessage.emit("INFO", "开始监控视频学习进度")
            
            # 启动定时器监控
            self.monitor_timer.start(self.monitor_interval)
            
            while self.monitoring:
                try:
                    # 检查是否有未完成的课件
                    has_incomplete = await self.check_incomplete_courseware()
                    
                    if not has_incomplete:
                        # 所有课件已完成
                        await self.mark_course_completed()
                        self.stop_monitoring()
                        return True
                    
                    # 获取视频播放进度
                    video_progress = await self.get_video_progress()
                    if video_progress:
                        await self.update_learning_progress(video_progress)
                    
                    # 检查是否需要处理异常情况
                    await self.handle_learning_exceptions()
                    
                    # 等待一段时间后继续检查
                    await asyncio.sleep(30)
                    
                except Exception as e:
                    self.logMessage.emit("WARNING", f"视频监控循环出错: {str(e)}")
                    await asyncio.sleep(10)
            
            return False
            
        except Exception as e:
            error_msg = f"视频学习监控失败: {str(e)}"
            self.errorOccurred.emit("MONITOR_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            self.stop_monitoring()
            return False
    
    async def check_incomplete_courseware(self) -> bool:
        """
        检查是否有未完成的课件
        
        Returns:
            bool: 是否有未完成的课件
        """
        try:
            # 检查页面中是否有"未完成"标识
            has_incomplete = await self.engine.page.evaluate("""
                const elements = document.querySelectorAll('span[title], .status-text, .progress-text');
                return Array.from(elements).some(el => 
                    el.textContent.includes('未完成') || 
                    el.textContent.includes('进行中') ||
                    el.textContent.includes('学习中')
                );
            """)
            
            return has_incomplete
            
        except Exception as e:
            self.logMessage.emit("WARNING", f"检查课件完成状态失败: {str(e)}")
            return True  # 出错时假设还有未完成的
    
    async def get_video_progress(self) -> Dict[str, Any]:
        """
        获取视频播放进度
        
        Returns:
            Dict[str, Any]: 视频进度信息
        """
        try:
            video_info = await self.engine.page.evaluate("""
                const video = document.querySelector('video');
                if (video) {
                    return {
                        currentTime: video.currentTime,
                        duration: video.duration,
                        paused: video.paused,
                        ended: video.ended,
                        readyState: video.readyState,
                        networkState: video.networkState
                    };
                }
                return null;
            """)
            
            if video_info and video_info['duration'] > 0:
                progress = (video_info['currentTime'] / video_info['duration']) * 100
                
                return {
                    'currentTime': video_info['currentTime'],
                    'duration': video_info['duration'],
                    'progress': progress,
                    'paused': video_info['paused'],
                    'ended': video_info['ended'],
                    'readyState': video_info['readyState'],
                    'networkState': video_info['networkState'],
                    'timestamp': time.time()
                }
            
            return {}
            
        except Exception as e:
            self.logMessage.emit("WARNING", f"获取视频进度失败: {str(e)}")
            return {}
    
    async def update_learning_progress(self, progress_data: Dict[str, Any]):
        """
        更新学习进度
        
        Args:
            progress_data: 进度数据
        """
        try:
            if not progress_data or not self.current_course:
                return
            
            current_progress = progress_data.get('progress', 0)
            current_time = progress_data.get('timestamp', time.time())
            
            # 检查进度是否有变化
            if abs(current_progress - self.last_progress) < 0.1:
                self.stuck_count += 1
                if self.stuck_count >= self.max_stuck_count:
                    self.logMessage.emit("WARNING", f"视频进度可能卡住，当前进度: {current_progress:.1f}%")
            else:
                self.stuck_count = 0
            
            self.last_progress = current_progress
            self.last_check_time = current_time
            
            # 发送进度更新信号
            self.progressUpdated.emit(self.current_course, progress_data)
            
            # 记录进度日志
            if current_progress > 0:
                self.logMessage.emit("DEBUG", 
                    f"课程 {self.current_course.get('name', '')} 学习进度: {current_progress:.1f}%")
            
        except Exception as e:
            self.logMessage.emit("WARNING", f"更新学习进度失败: {str(e)}")
    
    async def handle_learning_exceptions(self):
        """
        处理学习过程中的异常情况
        """
        try:
            # 检查是否有错误提示
            error_elements = await self.engine.page.query_selector_all('.error-message, .alert-danger, .el-message--error')
            if error_elements:
                for element in error_elements:
                    error_text = await element.text_content()
                    if error_text and error_text.strip():
                        self.logMessage.emit("WARNING", f"检测到错误提示: {error_text}")
            
            # 检查视频是否暂停
            video_progress = await self.get_video_progress()
            if video_progress and video_progress.get('paused', False):
                # 尝试恢复播放
                await self.engine.page.evaluate("document.querySelector('video')?.play()")
                self.logMessage.emit("DEBUG", "检测到视频暂停，尝试恢复播放")
            
        except Exception as e:
            self.logMessage.emit("WARNING", f"处理学习异常失败: {str(e)}")
    
    def _check_video_progress(self):
        """
        定时器回调：检查视频进度
        """
        if self.monitoring and self.current_course:
            # 这里可以添加定时检查逻辑
            pass
    
    async def mark_course_completed(self):
        """
        标记课程为已完成
        """
        try:
            if not self.current_course:
                return
            
            # 更新课程状态
            self.current_course['completed'] = '1'
            self.current_course['completed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 更新数据库
            course_dao.update_course_status(
                self.current_course.get('user_phone', ''),
                self.current_course.get('id', ''),
                '1',
                self.current_course['completed_date']
            )
            
            # 发送完成信号
            self.courseCompleted.emit(self.current_course)
            
            self.logMessage.emit("INFO", f"课程 {self.current_course.get('name', '')} 学习完成")
            
        except Exception as e:
            error_msg = f"标记课程完成失败: {str(e)}"
            self.errorOccurred.emit("MARK_COMPLETE_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
    
    def stop_monitoring(self):
        """
        停止监控
        """
        self.monitoring = False
        if self.monitor_timer.isActive():
            self.monitor_timer.stop()
        
        if self.current_course:
            self.logMessage.emit("INFO", f"停止监控课程: {self.current_course.get('name', '')}")
        
        self.current_course = None
        self.last_progress = 0
        self.stuck_count = 0
    
    def is_monitoring(self) -> bool:
        """
        检查是否正在监控
        
        Returns:
            bool: 是否正在监控
        """
        return self.monitoring
    
    def get_current_course(self) -> Optional[Dict[str, Any]]:
        """
        获取当前监控的课程
        
        Returns:
            Optional[Dict[str, Any]]: 当前课程信息
        """
        return self.current_course
