# coding: utf-8
"""
学习工具配置模块

该模块定义了学习工具的配置管理系统，包括系统设置、浏览器配置、
OCR识别、API捕获、网站配置和日志设置等各种配置选项。

主要功能：
- 定义学习工具的各种配置项
- 提供配置文件加载和保存
- 管理学习策略、并发控制等设置
- 支持配置验证和默认值设置

类说明：
- StudyConfig: 学习工具配置类
"""

from qfluentwidgets import (QConfig, ConfigItem, OptionsConfigItem, BoolValidator,
                            OptionsValidator, RangeConfigItem, RangeValidator, qconfig)


class StudyConfig(QConfig):
    """
    学习工具配置类

    定义学习工具的所有配置项，包括系统设置、浏览器配置、
    OCR识别、API捕获、网站配置和日志设置等各种配置选项。
    """

    # 系统配置
    asyncLogin = ConfigItem("System", "AsyncLogin", True, BoolValidator())
    compulsoryCourses = RangeConfigItem("System", "CompulsoryCourses", 20, RangeValidator(1, 100))
    electiveCourses = RangeConfigItem("System", "ElectiveCourses", 20, RangeValidator(1, 100))
    concurrentCount = RangeConfigItem("System", "ConcurrentCount", 2, RangeValidator(1, 10))
    delayTime = RangeConfigItem("System", "DelayTime", 1, RangeValidator(0, 10))
    retryCount = RangeConfigItem("System", "RetryCount", 3, RangeValidator(1, 10))
    autoStart = ConfigItem("System", "AutoStart", False, BoolValidator())
    autoMinimize = ConfigItem("System", "AutoMinimize", True, BoolValidator())
    enableNotifications = ConfigItem("System", "EnableNotifications", True, BoolValidator())
    saveWindowState = ConfigItem("System", "SaveWindowState", True, BoolValidator())
    checkUpdatesOnStart = ConfigItem("System", "CheckUpdatesOnStart", True, BoolValidator())

    # 浏览器配置
    browserType = OptionsConfigItem("Browser", "BrowserType", "chrome",
                                   OptionsValidator(["chrome", "firefox", "edge", "webkit"]))
    headless = ConfigItem("Browser", "Headless", False, BoolValidator())
    disableImages = ConfigItem("Browser", "DisableImages", False, BoolValidator())
    disableJavaScript = ConfigItem("Browser", "DisableJavaScript", False, BoolValidator())
    disableCSS = ConfigItem("Browser", "DisableCSS", False, BoolValidator())
    userAgent = ConfigItem("Browser", "UserAgent", "")
    windowWidth = RangeConfigItem("Browser", "WindowWidth", 1920, RangeValidator(800, 3840))
    windowHeight = RangeConfigItem("Browser", "WindowHeight", 1080, RangeValidator(600, 2160))
    pageLoadTimeout = RangeConfigItem("Browser", "PageLoadTimeout", 30, RangeValidator(5, 120))
    elementWaitTimeout = RangeConfigItem("Browser", "ElementWaitTimeout", 10, RangeValidator(1, 60))
    downloadPath = ConfigItem("Browser", "DownloadPath", "downloads")
    userDataDir = ConfigItem("Browser", "UserDataDir", "")
    enableDevTools = ConfigItem("Browser", "EnableDevTools", False, BoolValidator())
    browserType = OptionsConfigItem("Browser", "BrowserType", "chromium",
                                   OptionsValidator(["chromium", "chrome", "firefox", "edge"]))
    ocrEngine = OptionsConfigItem("Browser", "OCREngine", "ddddocr",
                                 OptionsValidator(["ddddocr", "baidu"]))

    # OCR配置
    primaryEngine = OptionsConfigItem("OCR", "PrimaryEngine", "ddddocr",
                                     OptionsValidator(["ddddocr", "baidu", "tesseract", "paddleocr"]))
    fallbackEngine = OptionsConfigItem("OCR", "FallbackEngine", "baidu",
                                      OptionsValidator(["ddddocr", "baidu", "tesseract", "paddleocr", "none"]))
    baiduApiKey = ConfigItem("OCR", "BaiduApiKey", "")
    baiduSecretKey = ConfigItem("OCR", "BaiduSecretKey", "")
    ocrTimeout = RangeConfigItem("OCR", "Timeout", 10, RangeValidator(5, 60))
    ocrRetryCount = RangeConfigItem("OCR", "RetryCount", 3, RangeValidator(1, 10))
    enablePreprocessing = ConfigItem("OCR", "EnablePreprocessing", True, BoolValidator())
    imageEnhancement = ConfigItem("OCR", "ImageEnhancement", True, BoolValidator())
    confidenceThreshold = RangeConfigItem("OCR", "ConfidenceThreshold", 0.8, RangeValidator(0.1, 1.0))

    # API配置
    apiTimeout = RangeConfigItem("API", "Timeout", 30, RangeValidator(5, 120))
    apiRetryCount = RangeConfigItem("API", "RetryCount", 3, RangeValidator(1, 10))
    apiCaptureEnabled = ConfigItem("API", "CaptureEnabled", True, BoolValidator())

    # 网站配置
    baseUrl = ConfigItem("Website", "BaseUrl", "https://study.jxgbwlxy.gov.cn")
    loginUrl = ConfigItem("Website", "LoginUrl", "https://study.jxgbwlxy.gov.cn/index")

    # 学习策略配置
    studyMode = OptionsConfigItem("StudyStrategy", "StudyMode", "sequential",
                                 OptionsValidator(["sequential", "random", "priority"]))
    autoNextCourse = ConfigItem("StudyStrategy", "AutoNextCourse", True, BoolValidator())
    skipCompletedCourses = ConfigItem("StudyStrategy", "SkipCompletedCourses", True, BoolValidator())
    minStudyTimePerCourse = RangeConfigItem("StudyStrategy", "MinStudyTimePerCourse", 300, RangeValidator(60, 3600))  # 秒
    maxStudyTimePerCourse = RangeConfigItem("StudyStrategy", "MaxStudyTimePerCourse", 1800, RangeValidator(300, 7200))  # 秒
    studySpeedMultiplier = RangeConfigItem("StudyStrategy", "StudySpeedMultiplier", 1.0, RangeValidator(0.5, 3.0))
    enableRandomDelay = ConfigItem("StudyStrategy", "EnableRandomDelay", True, BoolValidator())
    randomDelayMin = RangeConfigItem("StudyStrategy", "RandomDelayMin", 1, RangeValidator(0, 30))  # 秒
    randomDelayMax = RangeConfigItem("StudyStrategy", "RandomDelayMax", 5, RangeValidator(1, 60))  # 秒
    compulsoryCourses = RangeConfigItem("StudyStrategy", "CompulsoryCourses", 10, RangeValidator(1, 100))
    electiveCourses = RangeConfigItem("StudyStrategy", "ElectiveCourses", 5, RangeValidator(0, 100))
    autoAddElective = ConfigItem("StudyStrategy", "AutoAddElective", True, BoolValidator())
    monitorInterval = RangeConfigItem("StudyStrategy", "MonitorInterval", 5.0, RangeValidator(1.0, 60.0))

    # 日志配置
    logLevel = OptionsConfigItem("Logging", "LogLevel", "INFO",
                                OptionsValidator(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]))
    logDir = ConfigItem("Logging", "LogDir", "logs")
    maxLogFileSize = RangeConfigItem("Logging", "MaxLogFileSize", 10485760, RangeValidator(1048576, 104857600))  # 1MB-100MB
    logBackupCount = RangeConfigItem("Logging", "LogBackupCount", 5, RangeValidator(1, 20))
    enableConsoleLog = ConfigItem("Logging", "EnableConsoleLog", True, BoolValidator())
    enableFileLog = ConfigItem("Logging", "EnableFileLog", True, BoolValidator())
    enableDatabaseLog = ConfigItem("Logging", "EnableDatabaseLog", True, BoolValidator())
    maxDays = RangeConfigItem("Logging", "MaxDays", 30, RangeValidator(1, 365))
    maxSizeMB = RangeConfigItem("Logging", "MaxSizeMB", 100, RangeValidator(1, 1000))

    # 并发控制配置
    maxConcurrentTasks = RangeConfigItem("Concurrency", "MaxConcurrentTasks", 10, RangeValidator(1, 50))
    maxConcurrentUsers = RangeConfigItem("Concurrency", "MaxConcurrentUsers", 5, RangeValidator(1, 20))
    maxWorkers = RangeConfigItem("Concurrency", "MaxWorkers", 4, RangeValidator(1, 16))
    maxQueueSize = RangeConfigItem("Concurrency", "MaxQueueSize", 100, RangeValidator(10, 1000))
    defaultTimeout = RangeConfigItem("Concurrency", "DefaultTimeout", 300, RangeValidator(30, 3600))
    apiRateLimit = RangeConfigItem("Concurrency", "ApiRateLimit", 60, RangeValidator(10, 1000))
    schedulerInterval = RangeConfigItem("Concurrency", "SchedulerInterval", 1.0, RangeValidator(0.1, 10.0))

    # 数据备份配置
    enableAutoBackup = ConfigItem("Backup", "EnableAutoBackup", True, BoolValidator())
    backupInterval = RangeConfigItem("Backup", "BackupInterval", 24, RangeValidator(1, 168))  # 小时
    maxBackupFiles = RangeConfigItem("Backup", "MaxBackupFiles", 10, RangeValidator(1, 100))
    backupPath = ConfigItem("Backup", "BackupPath", "backups")
    compressBackups = ConfigItem("Backup", "CompressBackups", True, BoolValidator())


# 创建学习工具配置实例
cfg = StudyConfig()
qconfig.load('data/config/study.json', cfg)