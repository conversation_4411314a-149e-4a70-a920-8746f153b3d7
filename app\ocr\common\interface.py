# coding: utf-8
"""
OCR识别接口模块

该模块定义了OCR识别引擎的抽象接口，为不同的OCR引擎提供统一的接口规范。

主要功能：
- 定义OCR引擎抽象接口
- 提供统一的识别方法规范
- 支持置信度获取

类说明：
- IOCREngine: OCR引擎抽象接口

作者: 小帅工具箱
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any


class IOCREngine(ABC):
    """
    OCR引擎抽象接口
    
    定义了OCR识别引擎的标准接口，所有OCR引擎实现都应该继承此接口。
    """
    
    @abstractmethod
    async def recognize(self, image_data: bytes) -> str:
        """
        识别验证码
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            str: 识别结果文本
            
        Raises:
            OCRException: 识别失败时抛出异常
        """
        pass
    
    @abstractmethod
    def get_confidence(self) -> float:
        """
        获取识别置信度
        
        Returns:
            float: 置信度值，范围0.0-1.0
        """
        pass
    
    @abstractmethod
    def get_engine_name(self) -> str:
        """
        获取引擎名称
        
        Returns:
            str: 引擎名称
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        pass
    
    @abstractmethod
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        初始化引擎
        
        Args:
            config: 引擎配置参数
            
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """
        清理引擎资源
        """
        pass


class OCRException(Exception):
    """
    OCR识别异常类
    
    用于表示OCR识别过程中发生的各种异常情况。
    """
    
    def __init__(self, message: str, engine_name: str = "", error_code: int = 0):
        """
        初始化OCR异常
        
        Args:
            message: 异常消息
            engine_name: 引擎名称
            error_code: 错误代码
        """
        super().__init__(message)
        self.engine_name = engine_name
        self.error_code = error_code
        self.message = message
    
    def __str__(self) -> str:
        """返回异常字符串表示"""
        if self.engine_name:
            return f"[{self.engine_name}] {self.message}"
        return self.message


class OCRResult:
    """
    OCR识别结果类
    
    封装OCR识别的结果信息，包括识别文本、置信度、引擎信息等。
    """
    
    def __init__(self, text: str, confidence: float, engine_name: str, 
                 processing_time: float = 0.0, metadata: Optional[Dict[str, Any]] = None):
        """
        初始化OCR结果
        
        Args:
            text: 识别的文本
            confidence: 置信度
            engine_name: 使用的引擎名称
            processing_time: 处理时间（秒）
            metadata: 额外的元数据
        """
        self.text = text
        self.confidence = confidence
        self.engine_name = engine_name
        self.processing_time = processing_time
        self.metadata = metadata or {}
    
    def is_valid(self, min_confidence: float = 0.5) -> bool:
        """
        检查结果是否有效
        
        Args:
            min_confidence: 最小置信度阈值
            
        Returns:
            bool: 结果是否有效
        """
        return (self.text and 
                len(self.text.strip()) > 0 and 
                self.confidence >= min_confidence)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 结果字典
        """
        return {
            'text': self.text,
            'confidence': self.confidence,
            'engine_name': self.engine_name,
            'processing_time': self.processing_time,
            'metadata': self.metadata
        }
    
    def __str__(self) -> str:
        """返回字符串表示"""
        return f"OCRResult(text='{self.text}', confidence={self.confidence:.2f}, engine='{self.engine_name}')"
    
    def __repr__(self) -> str:
        """返回详细字符串表示"""
        return (f"OCRResult(text='{self.text}', confidence={self.confidence:.2f}, "
                f"engine='{self.engine_name}', time={self.processing_time:.3f}s)")
