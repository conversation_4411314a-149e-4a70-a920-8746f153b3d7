# coding: utf-8
"""
学习设置界面模块

该模块定义了学习设置界面，提供学习工具的各项配置和参数设置功能。

主要功能：
- 系统配置设置
- 浏览器配置
- OCR配置
- API配置
- 学习策略配置

类说明：
- StudySettingInterface: 学习设置界面类

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QHBoxLayout, QScrollArea
from qfluentwidgets import (CardWidget, PrimaryPushButton,
                            PushButton, InfoBar, InfoBarPosition,
                            FluentIcon as FIF, PasswordLineEdit,
                            OptionsSettingCard, RangeSettingCard, SwitchSettingCard,
                            ExpandLayout, SettingCardGroup, LineEdit)

from ..common.config import cfg


class StudySettingInterface(QScrollArea):
    """
    学习设置界面类

    提供学习工具的各项配置和参数设置功能。
    """

    def __init__(self, parent=None):
        """
        初始化学习设置界面

        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("StudySettingInterface")

        # 创建主容器
        self.view = QWidget()
        self.expandLayout = ExpandLayout(self.view)

        # 初始化界面
        self.initUI()
        self.loadSettings()
        self.connectSignalToSlot()

    def initUI(self):
        """初始化用户界面"""

        # 创建设置组
        self.createSystemSettingsGroup()
        self.createBrowserSettingsGroup()
        self.createOCRSettingsGroup()
        self.createLearningSettingsGroup()

        # 创建操作按钮
        self.createActionButtons()

        # 设置滚动区域
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
    
    def createSystemSettingsGroup(self):
        """创建系统设置组"""
        self.systemGroup = SettingCardGroup("系统设置", self.view)

        # 异步登录开关
        self.asyncLoginCard = SwitchSettingCard(
            FIF.SYNC,
            "异步登录",
            "启用异步登录模式，提高登录效率",
            cfg.asyncLogin,
            self.systemGroup
        )

        # 并发数量设置
        self.concurrentCard = RangeSettingCard(
            cfg.concurrentCount,
            FIF.PEOPLE,
            "并发数量",
            "同时进行学习的用户数量",
            self.systemGroup
        )

        # 重试次数设置
        self.retryCard = RangeSettingCard(
            cfg.retryCount,
            FIF.SYNC,
            "重试次数",
            "学习失败时的最大重试次数",
            self.systemGroup
        )

        # 延迟时间设置
        self.delayCard = RangeSettingCard(
            cfg.delayTime,
            FIF.PAUSE_BOLD,
            "延迟时间",
            "操作间隔延迟时间（秒）",
            self.systemGroup
        )

        # 添加设置卡片
        self.systemGroup.addSettingCard(self.asyncLoginCard)
        self.systemGroup.addSettingCard(self.concurrentCard)
        self.systemGroup.addSettingCard(self.retryCard)
        self.systemGroup.addSettingCard(self.delayCard)

        self.expandLayout.addWidget(self.systemGroup)

    def createBrowserSettingsGroup(self):
        """创建浏览器设置组"""
        self.browserGroup = SettingCardGroup("浏览器设置", self.view)

        # 无头模式开关
        self.headlessCard = SwitchSettingCard(
            FIF.VIEW,
            "无头模式",
            "启用无头模式，浏览器在后台运行",
            cfg.headless,
            self.browserGroup
        )

        # 浏览器类型选择
        self.browserCard = OptionsSettingCard(
            cfg.browserType,
            FIF.GLOBE,
            "浏览器类型",
            "选择用于自动化的浏览器",
            texts=["Chromium", "Chrome", "Firefox", "Edge"],
            parent=self.browserGroup
        )

        # 用户数据目录 - 使用简单的LineEdit代替
        self.userDataCard = LineEdit()
        self.userDataCard.setPlaceholderText("浏览器用户数据存储目录")
        self.userDataCard.setText(cfg.userDataDir.value)

        # 添加设置卡片
        self.browserGroup.addSettingCard(self.headlessCard)
        self.browserGroup.addSettingCard(self.browserCard)
        # 暂时不添加用户数据目录卡片

        self.expandLayout.addWidget(self.browserGroup)

    def createOCRSettingsGroup(self):
        """创建OCR设置组"""
        self.ocrGroup = SettingCardGroup("OCR设置", self.view)

        # OCR引擎选择
        self.ocrEngineCard = OptionsSettingCard(
            cfg.ocrEngine,
            FIF.PHOTO,
            "OCR引擎",
            "选择验证码识别引擎",
            texts=["ddddocr", "baidu"],
            parent=self.ocrGroup
        )

        # 百度OCR API Key
        self.baiduApiKeyCard = LineEdit()
        self.baiduApiKeyCard.setPlaceholderText("请输入百度OCR API Key")

        # 百度OCR Secret Key
        self.baiduSecretKeyCard = PasswordLineEdit()
        self.baiduSecretKeyCard.setPlaceholderText("请输入百度OCR Secret Key")

        # 添加设置卡片
        self.ocrGroup.addSettingCard(self.ocrEngineCard)

        self.expandLayout.addWidget(self.ocrGroup)

    def createLearningSettingsGroup(self):
        """创建学习设置组"""
        self.learningGroup = SettingCardGroup("学习设置", self.view)

        # 必修课程数量
        self.compulsoryCard = RangeSettingCard(
            cfg.compulsoryCourses,
            FIF.EDUCATION,
            "必修课程数量",
            "需要学习的必修课程数量",
            self.learningGroup
        )

        # 选修课程数量
        self.electiveCard = RangeSettingCard(
            cfg.electiveCourses,
            FIF.LIBRARY,
            "选修课程数量",
            "需要学习的选修课程数量",
            self.learningGroup
        )

        # 自动添加选修课
        self.autoElectiveCard = SwitchSettingCard(
            FIF.ADD,
            "自动添加选修课",
            "自动添加选修课程到学习列表",
            cfg.autoAddElective,
            self.learningGroup
        )

        # 视频监控间隔
        self.monitorIntervalCard = RangeSettingCard(
            cfg.monitorInterval,
            FIF.STOP_WATCH,
            "监控间隔",
            "视频学习监控检查间隔（秒）",
            self.learningGroup
        )

        # 添加设置卡片
        self.learningGroup.addSettingCard(self.compulsoryCard)
        self.learningGroup.addSettingCard(self.electiveCard)
        self.learningGroup.addSettingCard(self.autoElectiveCard)
        self.learningGroup.addSettingCard(self.monitorIntervalCard)

        self.expandLayout.addWidget(self.learningGroup)

    def createActionButtons(self):
        """创建操作按钮"""
        self.actionGroup = SettingCardGroup("操作", self.view)

        # 按钮容器
        self.buttonCard = CardWidget(self.actionGroup)
        self.buttonLayout = QHBoxLayout(self.buttonCard)
        self.buttonLayout.setContentsMargins(20, 15, 20, 15)
        self.buttonLayout.setSpacing(15)

        # 应用设置按钮（配置会自动保存）
        self.saveBtn = PrimaryPushButton("应用设置", self.buttonCard)
        self.saveBtn.setIcon(FIF.ACCEPT)
        self.saveBtn.setFixedSize(120, 36)

        # 重置设置按钮
        self.resetBtn = PushButton("重置设置", self.buttonCard)
        self.resetBtn.setIcon(FIF.SYNC)
        self.resetBtn.setFixedSize(120, 36)

        # 导入设置按钮
        self.importBtn = PushButton("导入设置", self.buttonCard)
        self.importBtn.setIcon(FIF.FOLDER)
        self.importBtn.setFixedSize(120, 36)

        # 导出设置按钮
        self.exportBtn = PushButton("导出设置", self.buttonCard)
        self.exportBtn.setIcon(FIF.SHARE)
        self.exportBtn.setFixedSize(120, 36)

        # 添加到布局
        self.buttonLayout.addWidget(self.saveBtn)
        self.buttonLayout.addWidget(self.resetBtn)
        self.buttonLayout.addWidget(self.importBtn)
        self.buttonLayout.addWidget(self.exportBtn)
        self.buttonLayout.addStretch()

        self.actionGroup.addSettingCard(self.buttonCard)
        self.expandLayout.addWidget(self.actionGroup)

    def loadSettings(self):
        """加载设置"""
        try:
            # 设置已经通过配置项自动加载
            pass
        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"加载设置失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

    def saveSettings(self):
        """应用设置"""
        try:
            # QConfig 会自动保存配置到文件，这里只需要显示确认信息
            InfoBar.success(
                title="设置已应用",
                content="配置已自动保存到文件",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"应用设置失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

    def resetSettings(self):
        """重置设置"""
        try:
            # 重置所有配置项到默认值
            cfg.asyncLogin.value = True
            cfg.concurrentCount.value = 3
            cfg.retryCount.value = 3
            cfg.delayTime.value = 2.0
            cfg.headless.value = True
            cfg.browserType.value = "chromium"
            cfg.userDataDir.value = ""
            cfg.ocrEngine.value = "ddddocr"
            cfg.compulsoryCourses.value = 10
            cfg.electiveCourses.value = 5
            cfg.autoAddElective.value = True
            cfg.monitorInterval.value = 5.0

            InfoBar.success(
                title="成功",
                content="设置已重置为默认值",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"重置设置失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.saveBtn.clicked.connect(self.saveSettings)
        self.resetBtn.clicked.connect(self.resetSettings)
    


    


