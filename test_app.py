# coding:utf-8
"""
测试应用程序启动
"""

import sys
import traceback
from PySide6.QtWidgets import QApplication
from app.xueyuan.view.study_main_interface import StudyMainInterface

def main():
    try:
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = StudyMainInterface()
        window.show()
        
        print("应用程序启动成功")
        
        # 启动事件循环
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
