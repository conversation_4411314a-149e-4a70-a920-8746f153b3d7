/* 学习工具主界面样式 - 浅色主题 */

StudyMainInterface {
    background-color: transparent;
    border: none;
}

/* Pivot 导航样式 */
StudyMainInterface Pivot {
    background-color: transparent;
}

/* 内容区域样式 */
StudyMainInterface QStackedWidget {
    background-color: rgb(249, 249, 249);
    border: 1px solid rgb(229, 229, 229);
    border-radius: 8px;
}

/* 卡片样式 */
StudyMainInterface CardWidget {
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(229, 229, 229);
    border-radius: 8px;
}

/* 标题样式 */
StudyMainInterface TitleLabel {
    color: rgb(32, 32, 32);
    font-size: 20px;
    font-weight: 600;
}

/* 正文标签样式 */
StudyMainInterface BodyLabel {
    color: rgb(96, 96, 96);
    font-size: 14px;
}
