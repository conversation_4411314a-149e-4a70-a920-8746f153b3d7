# coding: utf-8
"""
课程管理器模块

该模块实现了课程数据的获取、解析和管理功能，支持必修课和选修课的获取与自动添加。

主要功能：
- 课程数据获取和解析
- 必修课程列表获取
- 选修课程列表获取
- 自动添加选修课程
- 课程数据缓存和验证

类说明：
- CourseManager: 课程管理器

作者: 小帅工具箱
版本: v1.0
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from PySide6.QtCore import QObject, Signal

from ..common.config import cfg
from ..database.dao import course_dao


class CourseManager(QObject):
    """
    课程管理器
    
    负责课程数据的获取、解析和管理，支持必修课和选修课的处理。
    """
    
    # 信号定义
    coursesLoaded = Signal(str, dict)  # 用户手机号, 课程数据
    courseAdded = Signal(str, dict)  # 课程类型, 课程信息
    progressChanged = Signal(str, int, int)  # 操作类型, 当前进度, 总进度
    errorOccurred = Signal(str, str)  # 错误类型, 错误消息
    logMessage = Signal(str, str)  # 日志级别, 消息内容
    
    def __init__(self, study_engine, parent=None):
        """
        初始化课程管理器
        
        Args:
            study_engine: 学习引擎实例
            parent: 父对象
        """
        super().__init__(parent)
        self.engine = study_engine
        self.current_user_phone = ""
        self.courses_cache = {}
        self.cache_timestamp = 0
        self.cache_validity_seconds = 3600  # 缓存有效期1小时
    
    async def load_or_fetch_courses(self, phone: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        加载或获取课程数据
        
        Args:
            phone: 用户手机号
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 课程数据字典
        """
        try:
            self.current_user_phone = phone
            self.logMessage.emit("INFO", f"开始获取用户 {phone} 的课程数据")
            
            # 检查数据库中是否已有有效的课程数据
            existing_courses = await self.get_courses_from_db(phone)
            
            if self.is_courses_data_valid(existing_courses):
                self.logMessage.emit("INFO", "使用数据库中的课程数据")
                self.coursesLoaded.emit(phone, existing_courses)
                return existing_courses
            
            # 重新获取课程数据
            self.logMessage.emit("INFO", "从服务器获取最新课程数据")
            fresh_courses = await self.fetch_fresh_courses()
            
            # 保存到数据库
            await self.save_courses_to_db(phone, fresh_courses)
            
            self.coursesLoaded.emit(phone, fresh_courses)
            return fresh_courses
            
        except Exception as e:
            error_msg = f"获取课程数据失败: {str(e)}"
            self.errorOccurred.emit("COURSE_FETCH_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return {"必修课": [], "选修课": []}
    
    async def get_courses_from_db(self, phone: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        从数据库获取课程数据
        
        Args:
            phone: 用户手机号
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 课程数据
        """
        try:
            compulsory_courses = course_dao.get_courses_by_type(phone, "必修课")
            elective_courses = course_dao.get_courses_by_type(phone, "选修课")
            
            return {
                "必修课": [course.to_dict() for course in compulsory_courses],
                "选修课": [course.to_dict() for course in elective_courses]
            }
        except Exception as e:
            self.logMessage.emit("WARNING", f"从数据库获取课程数据失败: {str(e)}")
            return {"必修课": [], "选修课": []}
    
    def is_courses_data_valid(self, courses_data: Dict[str, List]) -> bool:
        """
        检查课程数据是否有效
        
        Args:
            courses_data: 课程数据
            
        Returns:
            bool: 数据是否有效
        """
        if not courses_data or not isinstance(courses_data, dict):
            return False
        
        compulsory_courses = courses_data.get("必修课", [])
        elective_courses = courses_data.get("选修课", [])
        
        # 检查必修课数量
        required_compulsory = cfg.compulsoryCourses.value
        if len(compulsory_courses) < required_compulsory:
            return False
        
        # 检查选修课数量
        required_elective = cfg.electiveCourses.value
        if len(elective_courses) < required_elective:
            return False
        
        return True
    
    async def fetch_fresh_courses(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取最新的课程数据
        
        Returns:
            Dict[str, List[Dict[str, Any]]]: 课程数据
        """
        try:
            # 进入"我的课程"页面
            await self.engine.page.goto("https://study.jxgbwlxy.gov.cn/study/courseMine?id=0")
            await self.engine.page.wait_for_load_state("networkidle")
            
            self.logMessage.emit("INFO", "正在获取必修课程...")
            # 获取必修课程
            compulsory_courses = await self.fetch_compulsory_courses()
            
            self.logMessage.emit("INFO", "正在获取选修课程...")
            # 获取选修课程
            elective_courses = await self.fetch_elective_courses()
            
            # 检查选修课数量是否足够
            required_elective_count = cfg.electiveCourses.value
            current_elective_count = len(elective_courses)
            
            if current_elective_count < required_elective_count:
                self.logMessage.emit("INFO", f"选修课数量不足，需要添加 {required_elective_count - current_elective_count} 门课程")
                await self.add_elective_courses(required_elective_count - current_elective_count)
                # 重新获取选修课程
                elective_courses = await self.fetch_elective_courses()
            
            return {
                "必修课": compulsory_courses,
                "选修课": elective_courses
            }
            
        except Exception as e:
            error_msg = f"获取课程数据失败: {str(e)}"
            self.errorOccurred.emit("COURSE_FETCH_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return {"必修课": [], "选修课": []}
    
    async def fetch_compulsory_courses(self) -> List[Dict[str, Any]]:
        """
        获取必修课程
        
        Returns:
            List[Dict[str, Any]]: 必修课程列表
        """
        try:
            await self.engine.page.click("text=我的必修课")
            await self.engine.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)  # 等待API响应
            
            courses = []
            page_num = 1
            
            while True:
                # 等待API响应并获取数据
                api_data = self.engine.api_capture.get_compulsory_courses_data()
                
                if api_data and 'records' in api_data:
                    self.logMessage.emit("DEBUG", f"获取到第 {page_num} 页必修课程数据，共 {len(api_data['records'])} 门课程")
                    
                    for course in api_data['records']:
                        course_info = self.parse_course_data(course, "必修课")
                        courses.append(course_info)
                    
                    # 更新进度
                    self.progressChanged.emit("获取必修课程", page_num, api_data.get('pages', 1))
                    
                    # 检查是否有下一页
                    if api_data.get('current', 1) < api_data.get('pages', 1):
                        next_button = await self.engine.page.query_selector('.btn-next:not(.disabled)')
                        if next_button:
                            await next_button.click()
                            await self.engine.page.wait_for_load_state("networkidle")
                            await asyncio.sleep(2)
                            page_num += 1
                        else:
                            break
                    else:
                        break
                else:
                    self.logMessage.emit("WARNING", f"第 {page_num} 页必修课程API数据为空")
                    break
            
            self.logMessage.emit("INFO", f"成功获取 {len(courses)} 门必修课程")
            return courses
            
        except Exception as e:
            error_msg = f"获取必修课程失败: {str(e)}"
            self.errorOccurred.emit("COMPULSORY_FETCH_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return []
    
    async def fetch_elective_courses(self) -> List[Dict[str, Any]]:
        """
        获取选修课程
        
        Returns:
            List[Dict[str, Any]]: 选修课程列表
        """
        try:
            await self.engine.page.click("text=我的选修课")
            await self.engine.page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)  # 等待API响应
            
            courses = []
            page_num = 1
            
            while True:
                # 等待API响应并获取数据
                api_data = self.engine.api_capture.get_elective_courses_data()
                
                if api_data and 'records' in api_data:
                    self.logMessage.emit("DEBUG", f"获取到第 {page_num} 页选修课程数据，共 {len(api_data['records'])} 门课程")
                    
                    for course in api_data['records']:
                        course_info = self.parse_course_data(course, "选修课")
                        courses.append(course_info)
                    
                    # 更新进度
                    self.progressChanged.emit("获取选修课程", page_num, api_data.get('pages', 1))
                    
                    # 检查是否有下一页
                    if api_data.get('current', 1) < api_data.get('pages', 1):
                        next_button = await self.engine.page.query_selector('.btn-next:not(.disabled)')
                        if next_button:
                            await next_button.click()
                            await self.engine.page.wait_for_load_state("networkidle")
                            await asyncio.sleep(2)
                            page_num += 1
                        else:
                            break
                    else:
                        break
                else:
                    self.logMessage.emit("WARNING", f"第 {page_num} 页选修课程API数据为空")
                    break
            
            self.logMessage.emit("INFO", f"成功获取 {len(courses)} 门选修课程")
            return courses
            
        except Exception as e:
            error_msg = f"获取选修课程失败: {str(e)}"
            self.errorOccurred.emit("ELECTIVE_FETCH_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return []
    
    def parse_course_data(self, course_raw: Dict[str, Any], course_type: str) -> Dict[str, Any]:
        """
        解析课程数据
        
        Args:
            course_raw: 原始课程数据
            course_type: 课程类型
            
        Returns:
            Dict[str, Any]: 解析后的课程数据
        """
        courseware_id = course_raw.get("courseware", {}).get("id", "")
        
        return {
            "name": course_raw.get("name", ""),
            "completed": course_raw.get("completed", "0"),
            "id": course_raw.get("id", ""),
            "credit": course_raw.get("credit", 0),
            "percentage": course_raw.get("percentage", "0"),
            "coursewareId": courseware_id,
            "videoUrl": f"https://study.jxgbwlxy.gov.cn/video?id={courseware_id}" if courseware_id else "",
            "completed_date": "",
            "course_type": course_type,
            "user_phone": self.current_user_phone
        }

    async def add_elective_courses(self, required_count: int) -> bool:
        """
        自动添加选修课程

        Args:
            required_count: 需要添加的课程数量

        Returns:
            bool: 是否成功添加
        """
        try:
            self.logMessage.emit("INFO", f"开始自动添加 {required_count} 门选修课程")

            # 进入课程选择页面
            await self.engine.page.goto("https://study.jxgbwlxy.gov.cn/study/course-view")
            await self.engine.page.wait_for_load_state("networkidle")

            # 检查页面模式并切换到表格模式
            grid_button = await self.engine.page.query_selector('.el-button--danger.el-button--small.is-plain .el-icon-s-grid')
            if grid_button:
                await self.engine.page.click('.el-button--danger.el-button--small.is-plain')
                await self.engine.page.wait_for_timeout(1000)

            added_count = 0
            current_page = 1

            while added_count < required_count:
                # 查找添加课程按钮
                add_buttons = await self.engine.page.query_selector_all('.el-icon-circle-plus-outline')

                if not add_buttons:
                    self.logMessage.emit("WARNING", f"第 {current_page} 页未找到可添加的课程")
                    break

                for button in add_buttons:
                    if added_count >= required_count:
                        break

                    try:
                        await button.click()
                        await self.engine.page.wait_for_timeout(500)
                        added_count += 1

                        self.progressChanged.emit("添加选修课程", added_count, required_count)
                        self.logMessage.emit("DEBUG", f"成功添加第 {added_count} 门选修课程")

                    except Exception as e:
                        self.logMessage.emit("WARNING", f"添加课程失败: {str(e)}")
                        continue

                # 检查是否需要翻页
                if added_count < required_count:
                    next_button = await self.engine.page.query_selector('.btn-next:not(.disabled)')
                    if next_button:
                        await next_button.click()
                        await self.engine.page.wait_for_load_state("networkidle")
                        current_page += 1
                        self.logMessage.emit("DEBUG", f"翻到第 {current_page} 页继续添加课程")
                    else:
                        self.logMessage.emit("WARNING", "已到最后一页，无法继续添加课程")
                        break

            success = added_count >= required_count
            if success:
                self.logMessage.emit("INFO", f"成功添加 {added_count} 门选修课程")
            else:
                self.logMessage.emit("WARNING", f"仅添加了 {added_count} 门选修课程，未达到要求的 {required_count} 门")

            return success

        except Exception as e:
            error_msg = f"自动添加选修课程失败: {str(e)}"
            self.errorOccurred.emit("ADD_ELECTIVE_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False

    async def save_courses_to_db(self, phone: str, courses_data: Dict[str, List[Dict[str, Any]]]) -> bool:
        """
        保存课程数据到数据库

        Args:
            phone: 用户手机号
            courses_data: 课程数据

        Returns:
            bool: 是否保存成功
        """
        try:
            # 先删除旧的课程数据
            course_dao.delete_user_courses(phone)

            # 保存新的课程数据
            for course_type, courses in courses_data.items():
                for course in courses:
                    course['user_phone'] = phone
                    course['course_type'] = course_type
                    course_dao.create_course(course)

            self.logMessage.emit("INFO", f"成功保存用户 {phone} 的课程数据到数据库")
            return True

        except Exception as e:
            error_msg = f"保存课程数据到数据库失败: {str(e)}"
            self.errorOccurred.emit("SAVE_COURSE_ERROR", error_msg)
            self.logMessage.emit("ERROR", error_msg)
            return False

    def get_incomplete_courses(self, courses_data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        获取未完成的课程列表

        Args:
            courses_data: 课程数据

        Returns:
            List[Dict[str, Any]]: 未完成的课程列表
        """
        incomplete_courses = []

        for course_type, courses in courses_data.items():
            for course in courses:
                if course.get('completed', '0') == '0':  # 未完成的课程
                    incomplete_courses.append(course)

        return incomplete_courses

    def get_course_statistics(self, courses_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        获取课程统计信息

        Args:
            courses_data: 课程数据

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_courses": 0,
            "completed_courses": 0,
            "incomplete_courses": 0,
            "total_credits": 0,
            "completed_credits": 0,
            "compulsory_total": 0,
            "compulsory_completed": 0,
            "elective_total": 0,
            "elective_completed": 0
        }

        for course_type, courses in courses_data.items():
            for course in courses:
                stats["total_courses"] += 1
                stats["total_credits"] += course.get('credit', 0)

                if course.get('completed', '0') == '1':
                    stats["completed_courses"] += 1
                    stats["completed_credits"] += course.get('credit', 0)
                else:
                    stats["incomplete_courses"] += 1

                if course_type == "必修课":
                    stats["compulsory_total"] += 1
                    if course.get('completed', '0') == '1':
                        stats["compulsory_completed"] += 1
                elif course_type == "选修课":
                    stats["elective_total"] += 1
                    if course.get('completed', '0') == '1':
                        stats["elective_completed"] += 1

        return stats
