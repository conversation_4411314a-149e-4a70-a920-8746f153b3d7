# coding: utf-8
"""
百度OCR引擎模块

该模块实现了基于百度AI开放平台的OCR识别引擎，作为备选的验证码识别引擎。

主要功能：
- 实现百度OCR API调用
- 支持通用文字识别
- 提供API调用错误处理
- 支持配置API密钥

类说明：
- BaiduOCREngine: 百度OCR引擎实现

作者: 小帅工具箱
版本: v1.0
"""

import time
import base64
import asyncio
import aiohttp
from typing import Optional, Dict, Any

from ..common.interface import IOCREngine, OCRException, OCRResult


class BaiduOCREngine(IOCREngine):
    """
    百度OCR引擎实现
    
    基于百度AI开放平台的OCR识别引擎，支持通用文字识别功能。
    """
    
    def __init__(self):
        """初始化百度OCR引擎"""
        self.engine_name = "baidu_ocr"
        self.api_key = ""
        self.secret_key = ""
        self.access_token = ""
        self.token_expires_at = 0
        self.last_confidence = 0.0
        self.is_initialized = False
        
        # API配置
        self.config = {
            'timeout': 10,
            'max_retries': 3,
            'retry_delay': 1.0
        }
        
        # API URLs
        self.token_url = "https://aip.baidubce.com/oauth/2.0/token"
        self.ocr_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic"
    
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        初始化引擎
        
        Args:
            config: 引擎配置参数，应包含api_key和secret_key
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not config:
                raise OCRException("百度OCR配置不能为空", self.engine_name)
            
            self.api_key = config.get('api_key', '')
            self.secret_key = config.get('secret_key', '')
            
            if not self.api_key or not self.secret_key:
                raise OCRException("百度OCR API Key和Secret Key不能为空", self.engine_name)
            
            # 更新其他配置
            if 'timeout' in config:
                self.config['timeout'] = config['timeout']
            if 'max_retries' in config:
                self.config['max_retries'] = config['max_retries']
            
            # 获取访问令牌
            await self._get_access_token()
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            raise OCRException(f"百度OCR引擎初始化失败: {str(e)}", self.engine_name)
    
    async def _get_access_token(self) -> str:
        """
        获取百度API访问令牌
        
        Returns:
            str: 访问令牌
        """
        # 检查令牌是否仍然有效
        current_time = time.time()
        if self.access_token and current_time < self.token_expires_at:
            return self.access_token
        
        try:
            params = {
                'grant_type': 'client_credentials',
                'client_id': self.api_key,
                'client_secret': self.secret_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.token_url,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=self.config['timeout'])
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'access_token' in data:
                            self.access_token = data['access_token']
                            # 设置令牌过期时间（提前5分钟刷新）
                            expires_in = data.get('expires_in', 2592000)  # 默认30天
                            self.token_expires_at = current_time + expires_in - 300
                            return self.access_token
                        else:
                            raise OCRException(f"获取访问令牌失败: {data}", self.engine_name)
                    else:
                        error_text = await response.text()
                        raise OCRException(f"获取访问令牌失败: HTTP {response.status} - {error_text}", self.engine_name)
                        
        except aiohttp.ClientError as e:
            raise OCRException(f"网络请求失败: {str(e)}", self.engine_name)
        except Exception as e:
            raise OCRException(f"获取访问令牌异常: {str(e)}", self.engine_name)
    
    async def recognize(self, image_data: bytes) -> str:
        """
        识别验证码
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            str: 识别结果文本
            
        Raises:
            OCRException: 识别失败时抛出异常
        """
        if not self.is_initialized:
            raise OCRException("百度OCR引擎未初始化", self.engine_name)
        
        # 确保有有效的访问令牌
        await self._get_access_token()
        
        try:
            # 将图片转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 准备请求参数
            params = {
                'access_token': self.access_token
            }
            
            data = {
                'image': image_base64,
                'language_type': 'CHN_ENG',  # 中英文混合
                'detect_direction': 'false',  # 不检测朝向
                'detect_language': 'false',   # 不检测语言
                'probability': 'false'        # 不返回置信度
            }
            
            start_time = time.time()
            
            # 发送OCR请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.ocr_url,
                    params=params,
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=self.config['timeout'])
                ) as response:
                    processing_time = time.time() - start_time
                    
                    if response.status == 200:
                        result_data = await response.json()
                        return self._parse_ocr_result(result_data, processing_time)
                    else:
                        error_text = await response.text()
                        raise OCRException(f"百度OCR API调用失败: HTTP {response.status} - {error_text}", self.engine_name)
                        
        except aiohttp.ClientError as e:
            raise OCRException(f"网络请求失败: {str(e)}", self.engine_name)
        except Exception as e:
            raise OCRException(f"百度OCR识别失败: {str(e)}", self.engine_name)
    
    def _parse_ocr_result(self, result_data: Dict[str, Any], processing_time: float) -> str:
        """
        解析OCR识别结果
        
        Args:
            result_data: API返回的结果数据
            processing_time: 处理时间
            
        Returns:
            str: 识别的文本
        """
        if 'error_code' in result_data:
            error_msg = result_data.get('error_msg', '未知错误')
            raise OCRException(f"百度OCR API错误: {error_msg}", self.engine_name)
        
        words_result = result_data.get('words_result', [])
        if not words_result:
            self.last_confidence = 0.0
            return ""
        
        # 合并所有识别的文字
        text_parts = []
        total_confidence = 0.0
        
        for word_info in words_result:
            words = word_info.get('words', '').strip()
            if words:
                text_parts.append(words)
                # 百度OCR基础版本不返回置信度，使用默认值
                total_confidence += 0.85
        
        result_text = ''.join(text_parts)
        
        # 计算平均置信度
        if text_parts:
            self.last_confidence = total_confidence / len(text_parts)
        else:
            self.last_confidence = 0.0
        
        # 根据处理时间和结果质量调整置信度
        self.last_confidence = self._adjust_confidence(result_text, processing_time)
        
        return result_text
    
    def _adjust_confidence(self, result: str, processing_time: float) -> float:
        """
        调整置信度
        
        Args:
            result: 识别结果
            processing_time: 处理时间
            
        Returns:
            float: 调整后的置信度
        """
        confidence = self.last_confidence
        
        # 根据结果长度调整
        if len(result) >= 4:
            confidence += 0.05
        elif len(result) < 3:
            confidence -= 0.1
        
        # 根据处理时间调整
        if processing_time > 5.0:  # 网络请求较慢
            confidence -= 0.05
        
        # 检查结果质量
        if result.isalnum():  # 纯字母数字
            confidence += 0.05
        
        return max(0.0, min(1.0, confidence))
    
    def get_confidence(self) -> float:
        """
        获取识别置信度
        
        Returns:
            float: 置信度值，范围0.0-1.0
        """
        return self.last_confidence
    
    def get_engine_name(self) -> str:
        """
        获取引擎名称
        
        Returns:
            str: 引擎名称
        """
        return self.engine_name
    
    def is_available(self) -> bool:
        """
        检查引擎是否可用
        
        Returns:
            bool: 引擎是否可用
        """
        return (self.is_initialized and 
                bool(self.api_key) and 
                bool(self.secret_key))
    
    async def cleanup(self) -> None:
        """清理引擎资源"""
        self.access_token = ""
        self.token_expires_at = 0
        self.is_initialized = False
        self.last_confidence = 0.0
    
    def get_api_quota_info(self) -> Dict[str, Any]:
        """
        获取API配额信息（需要额外实现）
        
        Returns:
            Dict[str, Any]: 配额信息
        """
        return {
            'engine': self.engine_name,
            'api_key': self.api_key[:8] + "..." if self.api_key else "",
            'token_valid': time.time() < self.token_expires_at,
            'token_expires_at': self.token_expires_at
        }
